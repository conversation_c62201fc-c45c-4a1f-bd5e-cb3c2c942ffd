const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const { ModuleFederationPlugin } = require("webpack").container;

const packageJson = require("./package.json");

const sharedDeps = {
  react: {
    requiredVersion: packageJson.dependencies.react,
    singleton: true,
    eager: true,
  },
  "react-dom": {
    requiredVersion: packageJson.dependencies["react-dom"],
    singleton: true,
    eager: true,
  },
  "react-router-dom": {
    requiredVersion: packageJson.dependencies["react-router-dom"],
    singleton: true,
    eager: true,
  },
  "@reduxjs/toolkit": {
    requiredVersion: packageJson.dependencies["@reduxjs/toolkit"],
    singleton: true,
  },
  "react-redux": {
    requiredVersion: packageJson.dependencies["react-redux"],
    singleton: true,
  },
};

module.exports = (env, argv) => {
  const isProduction = argv.mode === "production";

  return {
    entry: "./src/index.ts",
    target: "web",
    mode: argv.mode || "development",

    resolve: {
      extensions: [".ts", ".tsx", ".js", ".jsx"],
      alias: {
        "~": path.resolve(__dirname, "app"),
      },
    },

    module: {
      rules: [
        {
          test: /\.(ts|tsx|js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
            options: {
              presets: [
                "@babel/preset-env",
                "@babel/preset-react",
                "@babel/preset-typescript",
              ],
            },
          },
        },
        {
          test: /\.css$/,
          use: [
            isProduction ? MiniCssExtractPlugin.loader : "style-loader",
            "css-loader",
            "postcss-loader",
          ],
        },
        {
          test: /\.(png|jpe?g|gif|svg|ico)$/,
          type: "asset/resource",
        },
      ],
    },

    plugins: [
      new ModuleFederationPlugin({
        name: "bot_ui",
        filename: "remoteEntry.js",
        exposes: {
          "./": "./app/bootstrap.tsx",
          "./MicroFrontendDemo": "./app/components/MicroFrontendDemo.tsx",
          "./App": "./app/root.tsx",
          "./routes": "./app/router/routes.ts",
          "./store": "./app/redux/store.ts",
          "./RouterExample": "./app/components/RouterExample.tsx",
          "./Home": "./app/routes/Home/index.tsx",
          "./Agents": "./app/routes/Agents/index.tsx",
          "./NewAgent": "./app/routes/Agents/new.tsx",
        },
        shared: sharedDeps,
      }),

      new HtmlWebpackPlugin({
        template: "./index.html",
        inject: true,
      }),

      ...(isProduction
        ? [
            new MiniCssExtractPlugin({
              filename: "assets/[name].[contenthash].css",
            }),
          ]
        : []),
    ],

    output: {
      path: path.resolve(__dirname, "dist"),
      filename: isProduction
        ? "assets/[name].[contenthash].js"
        : "assets/[name].js",
      chunkFilename: isProduction
        ? "assets/[name].[contenthash].js"
        : "assets/[name].js",
      publicPath: "auto",
      clean: true,
    },

    devServer: {
      port: 5173,
      hot: true,
      historyApiFallback: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods":
          "GET, POST, PUT, DELETE, PATCH, OPTIONS",
        "Access-Control-Allow-Headers":
          "X-Requested-With, content-type, Authorization",
      },
      static: {
        directory: path.join(__dirname, "public"),
      },
    },

    optimization: {
      splitChunks: {
        chunks: "async",
      },
    },

    devtool: isProduction ? "source-map" : "eval-source-map",
  };
};
