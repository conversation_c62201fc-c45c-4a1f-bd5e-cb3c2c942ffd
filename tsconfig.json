{"include": ["src/**/*", "app/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node"], "target": "ES2022", "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "baseUrl": ".", "paths": {"~/*": ["./app/*"]}, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "declaration": false, "sourceMap": true}}