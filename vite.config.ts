import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import federation from "@originjs/vite-plugin-federation";
import { dependencies } from "./package.json";
import react from "@vitejs/plugin-react"; // Proper React support
import tailwindcss from "@tailwindcss/vite";

const sharedDeps = {
  react: {
    requiredVersion: dependencies.react,
    singleton: true,
    eager: true,
  },
  "react-dom": {
    requiredVersion: dependencies["react-dom"],
    singleton: true,
    eager: true,
  },
  "react-router-dom": {
    requiredVersion: dependencies["react-router-dom"],
    singleton: true,
    eager: true,
  },
  "@reduxjs/toolkit": {
    requiredVersion: dependencies["@reduxjs/toolkit"],
    singleton: true,
  },
  "react-redux": {
    requiredVersion: dependencies["react-redux"],
    singleton: true,
  },
};

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    tsconfigPaths(),
    federation({
      name: "bot_ui",
      filename: "remoteEntry.js",
      exposes: {
        "./": "./app/bootstrap.tsx",
        "./MicroFrontendDemo":
          "./app/components/MicroFrontendDemo.tsx",
        "./App": "./app/root.tsx",
        "./routes": "./app/router/routes.ts",
        "./store": "./app/redux/store.ts",
        "./RouterExample": "./app/components/RouterExample.tsx",
        "./Home": "./app/routes/Home/index.tsx",
        "./Agents": "./app/routes/Agents/index.tsx",
        "./NewAgent": "./app/routes/Agents/new.tsx",
      },
      shared: sharedDeps,
    }),
  ],
  build: {
    target: "esnext",
    minify: false,
    cssCodeSplit: false,
    modulePreload: false,
  },
  server: {
    port: 5173,
    strictPort: true,
    cors: true, headers: {
    'Access-Control-Allow-Origin': '*',
  }
  },
 
preview: {
  port: 5173,
  strictPort: true,
  headers: {
    'Access-Control-Allow-Origin': '*',
  },
},
});
