const { merge } = require('webpack-merge');
const common = require('./webpack.config.js');

module.exports = (env, argv) => {
  const baseConfig = common(env, { ...argv, mode: 'production' });
  
  return merge(baseConfig, {
    mode: 'production',
    devtool: 'source-map',
    
    optimization: {
      ...baseConfig.optimization,
      minimize: true,
      sideEffects: false,
      usedExports: true,
    },
    
    performance: {
      hints: 'warning',
      maxEntrypointSize: 512000,
      maxAssetSize: 512000,
    },
  });
};
