const { merge } = require('webpack-merge');
const common = require('./webpack.config.js');

module.exports = (env, argv) => {
  const baseConfig = common(env, { ...argv, mode: 'development' });
  
  return merge(baseConfig, {
    mode: 'development',
    devtool: 'eval-source-map',
    
    devServer: {
      ...baseConfig.devServer,
      hot: true,
      liveReload: true,
      open: false,
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
    },
    
    optimization: {
      ...baseConfig.optimization,
      minimize: false,
    },
  });
};
