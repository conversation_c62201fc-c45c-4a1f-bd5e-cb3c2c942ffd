/**
 * Bootstrap file for the micro-frontend.
 * This file is used when the micro-frontend is loaded in standalone mode.
 * When loaded as a federated module, the host application will control the bootstrapping.
 */

import React from "react";
import ReactDOM from "react-dom/client";
import App from "./root";
import "./app.css";

// Check if we're running in standalone mode or as a federated module
const isFederated = window.location.search.includes("federated=true");

// Only render the app if we're in standalone mode
if (!isFederated) {
  const rootElement = document.getElementById("root");

  if (rootElement) {
    ReactDOM.createRoot(rootElement).render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  }
}

// Export the components and utilities for federation
export * from "./federation-entry";
