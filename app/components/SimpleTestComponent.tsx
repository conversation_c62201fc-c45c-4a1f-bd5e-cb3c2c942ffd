import React from "react";

/**
 * A very simple component to test the micro-frontend integration and Tailwind CSS
 * This component has minimal dependencies and should be easy to load
 */
export default function SimpleTestComponent() {
  return (
    <div className="p-5 m-5 border-2 border-blue-500 rounded-lg bg-blue-50">
      <h2 className="text-blue-600 text-xl font-bold mb-2">
        Simple Test Component
      </h2>
      <p className="text-gray-700 mb-2">
        This component is loaded from the Bot UI micro-frontend.
      </p>
      <p className="text-gray-700 mb-2">
        If you can see this styled with Tailwind, the integration is working!
      </p>
      <p className="text-gray-600 text-sm">
        Current time: {new Date().toLocaleTimeString()}
      </p>

      {/* Additional Tailwind test elements */}
      <div className="mt-4 p-3 bg-green-100 border border-green-300 rounded">
        <p className="text-green-800 font-medium">
          ✅ Tailwind CSS is working if this box is green!
        </p>
      </div>

      <div className="mt-2 flex space-x-2">
        <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
          Test Button
        </button>
        <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors">
          Another Button
        </button>
      </div>
    </div>
  );
}
