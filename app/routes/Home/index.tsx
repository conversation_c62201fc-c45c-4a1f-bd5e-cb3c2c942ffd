import React from "react";
import Layout from "../../components/layout/Layout";
import SearchAndFilters from "../../components/dashboard/SearchAndFilters";
import ChatbotGrid from "../../components/dashboard/ChatbotGrid";

export default function Home() {
  return (
    <Layout>
      <div className="min-h-screen bg-[#fafbfc] p-6">
        <div className="max-w-[1400px] mx-auto">
          {/* Tailwind CSS Test Section */}
          <div className="mb-8 p-6 bg-white rounded-lg shadow-md border">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">🎨 Tailwind CSS Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-green-100 border border-green-300 rounded-lg">
                <h3 className="font-semibold text-green-800">✅ Colors Working</h3>
                <p className="text-green-600 text-sm">If you see green styling, colors are working!</p>
              </div>
              <div className="p-4 bg-blue-100 border border-blue-300 rounded-lg">
                <h3 className="font-semibold text-blue-800">✅ Layout Working</h3>
                <p className="text-blue-600 text-sm">Grid and spacing utilities are functional!</p>
              </div>
              <div className="p-4 bg-purple-100 border border-purple-300 rounded-lg">
                <h3 className="font-semibold text-purple-800">✅ Components Working</h3>
                <p className="text-purple-600 text-sm">Rounded corners and shadows are applied!</p>
              </div>
            </div>
            <div className="mt-4 flex space-x-3">
              <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-200">
                Primary Button
              </button>
              <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors duration-200">
                Secondary Button
              </button>
            </div>
          </div>

          <SearchAndFilters />
          <div className="mt-6">
            <ChatbotGrid />
          </div>
        </div>
      </div>
    </Layout>
  );
}
