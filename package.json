{"name": "bot-ui", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "start": "vite preview --port 5173 --strictPort", "typecheck": "tsc"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "isbot": "^5.1.27", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^11.16.10", "react-redux": "^9.2.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.4.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.38", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}