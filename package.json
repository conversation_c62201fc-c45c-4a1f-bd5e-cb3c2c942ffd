{"name": "bot-ui", "private": true, "scripts": {"build": "webpack --config webpack.prod.js", "build:dev": "webpack --config webpack.dev.js", "dev": "webpack serve --config webpack.dev.js", "start": "webpack serve --config webpack.prod.js --port 5173", "typecheck": "tsc"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "isbot": "^5.1.27", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^11.16.10", "react-redux": "^9.2.0", "react-router-dom": "^6.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.17", "babel-loader": "^9.1.3", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.9.1", "postcss": "^8.4.38", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tailwindcss": "^4.1.4", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-merge": "^6.0.1"}}