/**
 * Main entry point for the Webpack build
 * This file bootstraps the React application
 */

// Import CSS first
import("../app/app.css");

import("../app/bootstrap")
  .then((module) => {
    // The bootstrap module should handle the React app initialization
    console.log("Bot UI micro-frontend loaded successfully");
  })
  .catch((error) => {
    console.error("Failed to load Bot UI micro-frontend:", error);
  });
