"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["vendors-node_modules_reduxjs_toolkit_dist_query_rtk-query_modern_mjs"],{

/***/ "./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NamedSchemaError: () => (/* binding */ NamedSchemaError),\n/* harmony export */   QueryStatus: () => (/* binding */ QueryStatus),\n/* harmony export */   _NEVER: () => (/* binding */ _NEVER),\n/* harmony export */   buildCreateApi: () => (/* binding */ buildCreateApi),\n/* harmony export */   copyWithStructuralSharing: () => (/* binding */ copyWithStructuralSharing),\n/* harmony export */   coreModule: () => (/* binding */ coreModule),\n/* harmony export */   coreModuleName: () => (/* binding */ coreModuleName),\n/* harmony export */   createApi: () => (/* binding */ createApi),\n/* harmony export */   defaultSerializeQueryArgs: () => (/* binding */ defaultSerializeQueryArgs),\n/* harmony export */   fakeBaseQuery: () => (/* binding */ fakeBaseQuery),\n/* harmony export */   fetchBaseQuery: () => (/* binding */ fetchBaseQuery),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   setupListeners: () => (/* binding */ setupListeners),\n/* harmony export */   skipToken: () => (/* binding */ skipToken)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"webpack/sharing/consume/default/@reduxjs/toolkit/@reduxjs/toolkit\");\n/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immer */ \"./node_modules/immer/dist/immer.mjs\");\n/* harmony import */ var _standard_schema_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @standard-schema/utils */ \"./node_modules/@standard-schema/utils/dist/index.js\");\n/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reselect */ \"./node_modules/reselect/dist/reselect.mjs\");\n// src/query/core/apiState.ts\nvar QueryStatus = /* @__PURE__ */ ((QueryStatus2) => {\n  QueryStatus2[\"uninitialized\"] = \"uninitialized\";\n  QueryStatus2[\"pending\"] = \"pending\";\n  QueryStatus2[\"fulfilled\"] = \"fulfilled\";\n  QueryStatus2[\"rejected\"] = \"rejected\";\n  return QueryStatus2;\n})(QueryStatus || {});\nfunction getRequestStatusFlags(status) {\n  return {\n    status,\n    isUninitialized: status === \"uninitialized\" /* uninitialized */,\n    isLoading: status === \"pending\" /* pending */,\n    isSuccess: status === \"fulfilled\" /* fulfilled */,\n    isError: status === \"rejected\" /* rejected */\n  };\n}\n\n// src/query/core/rtkImports.ts\n\n\n// src/query/utils/copyWithStructuralSharing.ts\nvar isPlainObject2 = _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPlainObject;\nfunction copyWithStructuralSharing(oldObj, newObj) {\n  if (oldObj === newObj || !(isPlainObject2(oldObj) && isPlainObject2(newObj) || Array.isArray(oldObj) && Array.isArray(newObj))) {\n    return newObj;\n  }\n  const newKeys = Object.keys(newObj);\n  const oldKeys = Object.keys(oldObj);\n  let isSameObject = newKeys.length === oldKeys.length;\n  const mergeObj = Array.isArray(newObj) ? [] : {};\n  for (const key of newKeys) {\n    mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key]);\n    if (isSameObject) isSameObject = oldObj[key] === mergeObj[key];\n  }\n  return isSameObject ? oldObj : mergeObj;\n}\n\n// src/query/utils/countObjectKeys.ts\nfunction countObjectKeys(obj) {\n  let count = 0;\n  for (const _key in obj) {\n    count++;\n  }\n  return count;\n}\n\n// src/query/utils/flatten.ts\nvar flatten = (arr) => [].concat(...arr);\n\n// src/query/utils/isAbsoluteUrl.ts\nfunction isAbsoluteUrl(url) {\n  return new RegExp(`(^|:)//`).test(url);\n}\n\n// src/query/utils/isDocumentVisible.ts\nfunction isDocumentVisible() {\n  if (typeof document === \"undefined\") {\n    return true;\n  }\n  return document.visibilityState !== \"hidden\";\n}\n\n// src/query/utils/isNotNullish.ts\nfunction isNotNullish(v) {\n  return v != null;\n}\n\n// src/query/utils/isOnline.ts\nfunction isOnline() {\n  return typeof navigator === \"undefined\" ? true : navigator.onLine === void 0 ? true : navigator.onLine;\n}\n\n// src/query/utils/joinUrls.ts\nvar withoutTrailingSlash = (url) => url.replace(/\\/$/, \"\");\nvar withoutLeadingSlash = (url) => url.replace(/^\\//, \"\");\nfunction joinUrls(base, url) {\n  if (!base) {\n    return url;\n  }\n  if (!url) {\n    return base;\n  }\n  if (isAbsoluteUrl(url)) {\n    return url;\n  }\n  const delimiter = base.endsWith(\"/\") || !url.startsWith(\"?\") ? \"/\" : \"\";\n  base = withoutTrailingSlash(base);\n  url = withoutLeadingSlash(url);\n  return `${base}${delimiter}${url}`;\n}\n\n// src/query/utils/getOrInsert.ts\nfunction getOrInsert(map, key, value) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, value).get(key);\n}\n\n// src/query/fetchBaseQuery.ts\nvar defaultFetchFn = (...args) => fetch(...args);\nvar defaultValidateStatus = (response) => response.status >= 200 && response.status <= 299;\nvar defaultIsJsonContentType = (headers) => (\n  /*applicat*/\n  /ion\\/(vnd\\.api\\+)?json/.test(headers.get(\"content-type\") || \"\")\n);\nfunction stripUndefined(obj) {\n  if (!(0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(obj)) {\n    return obj;\n  }\n  const copy = {\n    ...obj\n  };\n  for (const [k, v] of Object.entries(copy)) {\n    if (v === void 0) delete copy[k];\n  }\n  return copy;\n}\nfunction fetchBaseQuery({\n  baseUrl,\n  prepareHeaders = (x) => x,\n  fetchFn = defaultFetchFn,\n  paramsSerializer,\n  isJsonContentType = defaultIsJsonContentType,\n  jsonContentType = \"application/json\",\n  jsonReplacer,\n  timeout: defaultTimeout,\n  responseHandler: globalResponseHandler,\n  validateStatus: globalValidateStatus,\n  ...baseFetchOptions\n} = {}) {\n  if (typeof fetch === \"undefined\" && fetchFn === defaultFetchFn) {\n    console.warn(\"Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.\");\n  }\n  return async (arg, api, extraOptions) => {\n    const {\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    } = api;\n    let meta;\n    let {\n      url,\n      headers = new Headers(baseFetchOptions.headers),\n      params = void 0,\n      responseHandler = globalResponseHandler ?? \"json\",\n      validateStatus = globalValidateStatus ?? defaultValidateStatus,\n      timeout = defaultTimeout,\n      ...rest\n    } = typeof arg == \"string\" ? {\n      url: arg\n    } : arg;\n    let abortController, signal = api.signal;\n    if (timeout) {\n      abortController = new AbortController();\n      api.signal.addEventListener(\"abort\", abortController.abort);\n      signal = abortController.signal;\n    }\n    let config = {\n      ...baseFetchOptions,\n      signal,\n      ...rest\n    };\n    headers = new Headers(stripUndefined(headers));\n    config.headers = await prepareHeaders(headers, {\n      getState,\n      arg,\n      extra,\n      endpoint,\n      forced,\n      type,\n      extraOptions\n    }) || headers;\n    const isJsonifiable = (body) => typeof body === \"object\" && ((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(body) || Array.isArray(body) || typeof body.toJSON === \"function\");\n    if (!config.headers.has(\"content-type\") && isJsonifiable(config.body)) {\n      config.headers.set(\"content-type\", jsonContentType);\n    }\n    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\n      config.body = JSON.stringify(config.body, jsonReplacer);\n    }\n    if (params) {\n      const divider = ~url.indexOf(\"?\") ? \"&\" : \"?\";\n      const query = paramsSerializer ? paramsSerializer(params) : new URLSearchParams(stripUndefined(params));\n      url += divider + query;\n    }\n    url = joinUrls(baseUrl, url);\n    const request = new Request(url, config);\n    const requestClone = new Request(url, config);\n    meta = {\n      request: requestClone\n    };\n    let response, timedOut = false, timeoutId = abortController && setTimeout(() => {\n      timedOut = true;\n      abortController.abort();\n    }, timeout);\n    try {\n      response = await fetchFn(request);\n    } catch (e) {\n      return {\n        error: {\n          status: timedOut ? \"TIMEOUT_ERROR\" : \"FETCH_ERROR\",\n          error: String(e)\n        },\n        meta\n      };\n    } finally {\n      if (timeoutId) clearTimeout(timeoutId);\n      abortController?.signal.removeEventListener(\"abort\", abortController.abort);\n    }\n    const responseClone = response.clone();\n    meta.response = responseClone;\n    let resultData;\n    let responseText = \"\";\n    try {\n      let handleResponseError;\n      await Promise.all([\n        handleResponse(response, responseHandler).then((r) => resultData = r, (e) => handleResponseError = e),\n        // see https://github.com/node-fetch/node-fetch/issues/665#issuecomment-538995182\n        // we *have* to \"use up\" both streams at the same time or they will stop running in node-fetch scenarios\n        responseClone.text().then((r) => responseText = r, () => {\n        })\n      ]);\n      if (handleResponseError) throw handleResponseError;\n    } catch (e) {\n      return {\n        error: {\n          status: \"PARSING_ERROR\",\n          originalStatus: response.status,\n          data: responseText,\n          error: String(e)\n        },\n        meta\n      };\n    }\n    return validateStatus(response, resultData) ? {\n      data: resultData,\n      meta\n    } : {\n      error: {\n        status: response.status,\n        data: resultData\n      },\n      meta\n    };\n  };\n  async function handleResponse(response, responseHandler) {\n    if (typeof responseHandler === \"function\") {\n      return responseHandler(response);\n    }\n    if (responseHandler === \"content-type\") {\n      responseHandler = isJsonContentType(response.headers) ? \"json\" : \"text\";\n    }\n    if (responseHandler === \"json\") {\n      const text = await response.text();\n      return text.length ? JSON.parse(text) : null;\n    }\n    return response.text();\n  }\n}\n\n// src/query/HandledError.ts\nvar HandledError = class {\n  constructor(value, meta = void 0) {\n    this.value = value;\n    this.meta = meta;\n  }\n};\n\n// src/query/retry.ts\nasync function defaultBackoff(attempt = 0, maxRetries = 5) {\n  const attempts = Math.min(attempt, maxRetries);\n  const timeout = ~~((Math.random() + 0.4) * (300 << attempts));\n  await new Promise((resolve) => setTimeout((res) => resolve(res), timeout));\n}\nfunction fail(error, meta) {\n  throw Object.assign(new HandledError({\n    error,\n    meta\n  }), {\n    throwImmediately: true\n  });\n}\nvar EMPTY_OPTIONS = {};\nvar retryWithBackoff = (baseQuery, defaultOptions) => async (args, api, extraOptions) => {\n  const possibleMaxRetries = [5, (defaultOptions || EMPTY_OPTIONS).maxRetries, (extraOptions || EMPTY_OPTIONS).maxRetries].filter((x) => x !== void 0);\n  const [maxRetries] = possibleMaxRetries.slice(-1);\n  const defaultRetryCondition = (_, __, {\n    attempt\n  }) => attempt <= maxRetries;\n  const options = {\n    maxRetries,\n    backoff: defaultBackoff,\n    retryCondition: defaultRetryCondition,\n    ...defaultOptions,\n    ...extraOptions\n  };\n  let retry2 = 0;\n  while (true) {\n    try {\n      const result = await baseQuery(args, api, extraOptions);\n      if (result.error) {\n        throw new HandledError(result);\n      }\n      return result;\n    } catch (e) {\n      retry2++;\n      if (e.throwImmediately) {\n        if (e instanceof HandledError) {\n          return e.value;\n        }\n        throw e;\n      }\n      if (e instanceof HandledError && !options.retryCondition(e.value.error, args, {\n        attempt: retry2,\n        baseQueryApi: api,\n        extraOptions\n      })) {\n        return e.value;\n      }\n      await options.backoff(retry2, options.maxRetries);\n    }\n  }\n};\nvar retry = /* @__PURE__ */ Object.assign(retryWithBackoff, {\n  fail\n});\n\n// src/query/core/setupListeners.ts\nvar onFocus = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(\"__rtkq/focused\");\nvar onFocusLost = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(\"__rtkq/unfocused\");\nvar onOnline = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(\"__rtkq/online\");\nvar onOffline = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(\"__rtkq/offline\");\nvar initialized = false;\nfunction setupListeners(dispatch, customHandler) {\n  function defaultHandler() {\n    const handleFocus = () => dispatch(onFocus());\n    const handleFocusLost = () => dispatch(onFocusLost());\n    const handleOnline = () => dispatch(onOnline());\n    const handleOffline = () => dispatch(onOffline());\n    const handleVisibilityChange = () => {\n      if (window.document.visibilityState === \"visible\") {\n        handleFocus();\n      } else {\n        handleFocusLost();\n      }\n    };\n    if (!initialized) {\n      if (typeof window !== \"undefined\" && window.addEventListener) {\n        window.addEventListener(\"visibilitychange\", handleVisibilityChange, false);\n        window.addEventListener(\"focus\", handleFocus, false);\n        window.addEventListener(\"online\", handleOnline, false);\n        window.addEventListener(\"offline\", handleOffline, false);\n        initialized = true;\n      }\n    }\n    const unsubscribe = () => {\n      window.removeEventListener(\"focus\", handleFocus);\n      window.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n      window.removeEventListener(\"online\", handleOnline);\n      window.removeEventListener(\"offline\", handleOffline);\n      initialized = false;\n    };\n    return unsubscribe;\n  }\n  return customHandler ? customHandler(dispatch, {\n    onFocus,\n    onFocusLost,\n    onOffline,\n    onOnline\n  }) : defaultHandler();\n}\n\n// src/query/endpointDefinitions.ts\nfunction isQueryDefinition(e) {\n  return e.type === \"query\" /* query */;\n}\nfunction isMutationDefinition(e) {\n  return e.type === \"mutation\" /* mutation */;\n}\nfunction isInfiniteQueryDefinition(e) {\n  return e.type === \"infinitequery\" /* infinitequery */;\n}\nfunction isAnyQueryDefinition(e) {\n  return isQueryDefinition(e) || isInfiniteQueryDefinition(e);\n}\nfunction calculateProvidedBy(description, result, error, queryArg, meta, assertTagTypes) {\n  if (isFunction(description)) {\n    return description(result, error, queryArg, meta).filter(isNotNullish).map(expandTagDescription).map(assertTagTypes);\n  }\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n  return [];\n}\nfunction isFunction(t) {\n  return typeof t === \"function\";\n}\nfunction expandTagDescription(description) {\n  return typeof description === \"string\" ? {\n    type: description\n  } : description;\n}\n\n// src/query/core/buildThunks.ts\n\n\n// src/query/core/buildInitiate.ts\n\n\n// src/tsHelpers.ts\nfunction asSafePromise(promise, fallback) {\n  return promise.catch(fallback);\n}\n\n// src/query/core/buildInitiate.ts\nvar forceQueryFnSymbol = Symbol(\"forceQueryFn\");\nvar isUpsertQuery = (arg) => typeof arg[forceQueryFnSymbol] === \"function\";\nfunction buildInitiate({\n  serializeQueryArgs,\n  queryThunk,\n  infiniteQueryThunk,\n  mutationThunk,\n  api,\n  context\n}) {\n  const runningQueries = /* @__PURE__ */ new Map();\n  const runningMutations = /* @__PURE__ */ new Map();\n  const {\n    unsubscribeQueryResult,\n    removeMutationResult,\n    updateSubscriptionOptions\n  } = api.internalActions;\n  return {\n    buildInitiateQuery,\n    buildInitiateInfiniteQuery,\n    buildInitiateMutation,\n    getRunningQueryThunk,\n    getRunningMutationThunk,\n    getRunningQueriesThunk,\n    getRunningMutationsThunk\n  };\n  function getRunningQueryThunk(endpointName, queryArgs) {\n    return (dispatch) => {\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      return runningQueries.get(dispatch)?.[queryCacheKey];\n    };\n  }\n  function getRunningMutationThunk(_endpointName, fixedCacheKeyOrRequestId) {\n    return (dispatch) => {\n      return runningMutations.get(dispatch)?.[fixedCacheKeyOrRequestId];\n    };\n  }\n  function getRunningQueriesThunk() {\n    return (dispatch) => Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish);\n  }\n  function getRunningMutationsThunk() {\n    return (dispatch) => Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish);\n  }\n  function middlewareWarning(dispatch) {\n    if (true) {\n      if (middlewareWarning.triggered) return;\n      const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n      middlewareWarning.triggered = true;\n      if (typeof returnedValue !== \"object\" || typeof returnedValue?.type === \"string\") {\n        throw new Error( false ? 0 : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\nYou must add the middleware for RTK-Query to function correctly!`);\n      }\n    }\n  }\n  function buildInitiateAnyQuery(endpointName, endpointDefinition) {\n    const queryAction = (arg, {\n      subscribe = true,\n      forceRefetch,\n      subscriptionOptions,\n      [forceQueryFnSymbol]: forceQueryFn,\n      ...rest\n    } = {}) => (dispatch, getState) => {\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs: arg,\n        endpointDefinition,\n        endpointName\n      });\n      let thunk;\n      const commonThunkArgs = {\n        ...rest,\n        type: \"query\",\n        subscribe,\n        forceRefetch,\n        subscriptionOptions,\n        endpointName,\n        originalArgs: arg,\n        queryCacheKey,\n        [forceQueryFnSymbol]: forceQueryFn\n      };\n      if (isQueryDefinition(endpointDefinition)) {\n        thunk = queryThunk(commonThunkArgs);\n      } else {\n        const {\n          direction,\n          initialPageParam\n        } = rest;\n        thunk = infiniteQueryThunk({\n          ...commonThunkArgs,\n          // Supply these even if undefined. This helps with a field existence\n          // check over in `buildSlice.ts`\n          direction,\n          initialPageParam\n        });\n      }\n      const selector = api.endpoints[endpointName].select(arg);\n      const thunkResult = dispatch(thunk);\n      const stateAfter = selector(getState());\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort\n      } = thunkResult;\n      const skippedSynchronously = stateAfter.requestId !== requestId;\n      const runningQuery = runningQueries.get(dispatch)?.[queryCacheKey];\n      const selectFromState = () => selector(getState());\n      const statePromise = Object.assign(forceQueryFn ? (\n        // a query has been forced (upsertQueryData)\n        // -> we want to resolve it once data has been written with the data that will be written\n        thunkResult.then(selectFromState)\n      ) : skippedSynchronously && !runningQuery ? (\n        // a query has been skipped due to a condition and we do not have any currently running query\n        // -> we want to resolve it immediately with the current data\n        Promise.resolve(stateAfter)\n      ) : (\n        // query just started or one is already in flight\n        // -> wait for the running query, then resolve with data from after that\n        Promise.all([runningQuery, thunkResult]).then(selectFromState)\n      ), {\n        arg,\n        requestId,\n        subscriptionOptions,\n        queryCacheKey,\n        abort,\n        async unwrap() {\n          const result = await statePromise;\n          if (result.isError) {\n            throw result.error;\n          }\n          return result.data;\n        },\n        refetch: () => dispatch(queryAction(arg, {\n          subscribe: false,\n          forceRefetch: true\n        })),\n        unsubscribe() {\n          if (subscribe) dispatch(unsubscribeQueryResult({\n            queryCacheKey,\n            requestId\n          }));\n        },\n        updateSubscriptionOptions(options) {\n          statePromise.subscriptionOptions = options;\n          dispatch(updateSubscriptionOptions({\n            endpointName,\n            requestId,\n            queryCacheKey,\n            options\n          }));\n        }\n      });\n      if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\n        const running = getOrInsert(runningQueries, dispatch, {});\n        running[queryCacheKey] = statePromise;\n        statePromise.then(() => {\n          delete running[queryCacheKey];\n          if (!countObjectKeys(running)) {\n            runningQueries.delete(dispatch);\n          }\n        });\n      }\n      return statePromise;\n    };\n    return queryAction;\n  }\n  function buildInitiateQuery(endpointName, endpointDefinition) {\n    const queryAction = buildInitiateAnyQuery(endpointName, endpointDefinition);\n    return queryAction;\n  }\n  function buildInitiateInfiniteQuery(endpointName, endpointDefinition) {\n    const infiniteQueryAction = buildInitiateAnyQuery(endpointName, endpointDefinition);\n    return infiniteQueryAction;\n  }\n  function buildInitiateMutation(endpointName) {\n    return (arg, {\n      track = true,\n      fixedCacheKey\n    } = {}) => (dispatch, getState) => {\n      const thunk = mutationThunk({\n        type: \"mutation\",\n        endpointName,\n        originalArgs: arg,\n        track,\n        fixedCacheKey\n      });\n      const thunkResult = dispatch(thunk);\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort,\n        unwrap\n      } = thunkResult;\n      const returnValuePromise = asSafePromise(thunkResult.unwrap().then((data) => ({\n        data\n      })), (error) => ({\n        error\n      }));\n      const reset = () => {\n        dispatch(removeMutationResult({\n          requestId,\n          fixedCacheKey\n        }));\n      };\n      const ret = Object.assign(returnValuePromise, {\n        arg: thunkResult.arg,\n        requestId,\n        abort,\n        unwrap,\n        reset\n      });\n      const running = runningMutations.get(dispatch) || {};\n      runningMutations.set(dispatch, running);\n      running[requestId] = ret;\n      ret.then(() => {\n        delete running[requestId];\n        if (!countObjectKeys(running)) {\n          runningMutations.delete(dispatch);\n        }\n      });\n      if (fixedCacheKey) {\n        running[fixedCacheKey] = ret;\n        ret.then(() => {\n          if (running[fixedCacheKey] === ret) {\n            delete running[fixedCacheKey];\n            if (!countObjectKeys(running)) {\n              runningMutations.delete(dispatch);\n            }\n          }\n        });\n      }\n      return ret;\n    };\n  }\n}\n\n// src/query/standardSchema.ts\n\nvar NamedSchemaError = class extends _standard_schema_utils__WEBPACK_IMPORTED_MODULE_1__.SchemaError {\n  constructor(issues, value, schemaName, _bqMeta) {\n    super(issues);\n    this.value = value;\n    this.schemaName = schemaName;\n    this._bqMeta = _bqMeta;\n  }\n};\nasync function parseWithSchema(schema, data, schemaName, bqMeta) {\n  const result = await schema[\"~standard\"].validate(data);\n  if (result.issues) {\n    throw new NamedSchemaError(result.issues, data, schemaName, bqMeta);\n  }\n  return result.value;\n}\n\n// src/query/core/buildThunks.ts\nfunction defaultTransformResponse(baseQueryReturnValue) {\n  return baseQueryReturnValue;\n}\nvar addShouldAutoBatch = (arg = {}) => {\n  return {\n    ...arg,\n    [_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.SHOULD_AUTOBATCH]: true\n  };\n};\nfunction buildThunks({\n  reducerPath,\n  baseQuery,\n  context: {\n    endpointDefinitions\n  },\n  serializeQueryArgs,\n  api,\n  assertTagType,\n  selectors,\n  onSchemaFailure,\n  catchSchemaFailure: globalCatchSchemaFailure,\n  skipSchemaValidation: globalSkipSchemaValidation\n}) {\n  const patchQueryData = (endpointName, arg, patches, updateProvided) => (dispatch, getState) => {\n    const endpointDefinition = endpointDefinitions[endpointName];\n    const queryCacheKey = serializeQueryArgs({\n      queryArgs: arg,\n      endpointDefinition,\n      endpointName\n    });\n    dispatch(api.internalActions.queryResultPatched({\n      queryCacheKey,\n      patches\n    }));\n    if (!updateProvided) {\n      return;\n    }\n    const newValue = api.endpoints[endpointName].select(arg)(\n      // Work around TS 4.1 mismatch\n      getState()\n    );\n    const providedTags = calculateProvidedBy(endpointDefinition.providesTags, newValue.data, void 0, arg, {}, assertTagType);\n    dispatch(api.internalActions.updateProvidedBy([{\n      queryCacheKey,\n      providedTags\n    }]));\n  };\n  function addToStart(items, item, max = 0) {\n    const newItems = [item, ...items];\n    return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n  }\n  function addToEnd(items, item, max = 0) {\n    const newItems = [...items, item];\n    return max && newItems.length > max ? newItems.slice(1) : newItems;\n  }\n  const updateQueryData = (endpointName, arg, updateRecipe, updateProvided = true) => (dispatch, getState) => {\n    const endpointDefinition = api.endpoints[endpointName];\n    const currentState = endpointDefinition.select(arg)(\n      // Work around TS 4.1 mismatch\n      getState()\n    );\n    const ret = {\n      patches: [],\n      inversePatches: [],\n      undo: () => dispatch(api.util.patchQueryData(endpointName, arg, ret.inversePatches, updateProvided))\n    };\n    if (currentState.status === \"uninitialized\" /* uninitialized */) {\n      return ret;\n    }\n    let newValue;\n    if (\"data\" in currentState) {\n      if ((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraftable)(currentState.data)) {\n        const [value, patches, inversePatches] = (0,immer__WEBPACK_IMPORTED_MODULE_2__.produceWithPatches)(currentState.data, updateRecipe);\n        ret.patches.push(...patches);\n        ret.inversePatches.push(...inversePatches);\n        newValue = value;\n      } else {\n        newValue = updateRecipe(currentState.data);\n        ret.patches.push({\n          op: \"replace\",\n          path: [],\n          value: newValue\n        });\n        ret.inversePatches.push({\n          op: \"replace\",\n          path: [],\n          value: currentState.data\n        });\n      }\n    }\n    if (ret.patches.length === 0) {\n      return ret;\n    }\n    dispatch(api.util.patchQueryData(endpointName, arg, ret.patches, updateProvided));\n    return ret;\n  };\n  const upsertQueryData = (endpointName, arg, value) => (dispatch) => {\n    const res = dispatch(api.endpoints[endpointName].initiate(arg, {\n      subscribe: false,\n      forceRefetch: true,\n      [forceQueryFnSymbol]: () => ({\n        data: value\n      })\n    }));\n    return res;\n  };\n  const getTransformCallbackForEndpoint = (endpointDefinition, transformFieldName) => {\n    return endpointDefinition.query && endpointDefinition[transformFieldName] ? endpointDefinition[transformFieldName] : defaultTransformResponse;\n  };\n  const executeEndpoint = async (arg, {\n    signal,\n    abort,\n    rejectWithValue,\n    fulfillWithValue,\n    dispatch,\n    getState,\n    extra\n  }) => {\n    const endpointDefinition = endpointDefinitions[arg.endpointName];\n    const {\n      metaSchema,\n      skipSchemaValidation = globalSkipSchemaValidation\n    } = endpointDefinition;\n    try {\n      let transformResponse = getTransformCallbackForEndpoint(endpointDefinition, \"transformResponse\");\n      const baseQueryApi = {\n        signal,\n        abort,\n        dispatch,\n        getState,\n        extra,\n        endpoint: arg.endpointName,\n        type: arg.type,\n        forced: arg.type === \"query\" ? isForcedQuery(arg, getState()) : void 0,\n        queryCacheKey: arg.type === \"query\" ? arg.queryCacheKey : void 0\n      };\n      const forceQueryFn = arg.type === \"query\" ? arg[forceQueryFnSymbol] : void 0;\n      let finalQueryReturnValue;\n      const fetchPage = async (data, param, maxPages, previous) => {\n        if (param == null && data.pages.length) {\n          return Promise.resolve({\n            data\n          });\n        }\n        const finalQueryArg = {\n          queryArg: arg.originalArgs,\n          pageParam: param\n        };\n        const pageResponse = await executeRequest(finalQueryArg);\n        const addTo = previous ? addToStart : addToEnd;\n        return {\n          data: {\n            pages: addTo(data.pages, pageResponse.data, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          },\n          meta: pageResponse.meta\n        };\n      };\n      async function executeRequest(finalQueryArg) {\n        let result;\n        const {\n          extraOptions,\n          argSchema,\n          rawResponseSchema,\n          responseSchema\n        } = endpointDefinition;\n        if (argSchema && !skipSchemaValidation) {\n          finalQueryArg = await parseWithSchema(\n            argSchema,\n            finalQueryArg,\n            \"argSchema\",\n            {}\n            // we don't have a meta yet, so we can't pass it\n          );\n        }\n        if (forceQueryFn) {\n          result = forceQueryFn();\n        } else if (endpointDefinition.query) {\n          result = await baseQuery(endpointDefinition.query(finalQueryArg), baseQueryApi, extraOptions);\n        } else {\n          result = await endpointDefinition.queryFn(finalQueryArg, baseQueryApi, extraOptions, (arg2) => baseQuery(arg2, baseQueryApi, extraOptions));\n        }\n        if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n          const what = endpointDefinition.query ? \"`baseQuery`\" : \"`queryFn`\";\n          let err;\n          if (!result) {\n            err = `${what} did not return anything.`;\n          } else if (typeof result !== \"object\") {\n            err = `${what} did not return an object.`;\n          } else if (result.error && result.data) {\n            err = `${what} returned an object containing both \\`error\\` and \\`result\\`.`;\n          } else if (result.error === void 0 && result.data === void 0) {\n            err = `${what} returned an object containing neither a valid \\`error\\` and \\`result\\`. At least one of them should not be \\`undefined\\``;\n          } else {\n            for (const key of Object.keys(result)) {\n              if (key !== \"error\" && key !== \"data\" && key !== \"meta\") {\n                err = `The object returned by ${what} has the unknown property ${key}.`;\n                break;\n              }\n            }\n          }\n          if (err) {\n            console.error(`Error encountered handling the endpoint ${arg.endpointName}.\n                  ${err}\n                  It needs to return an object with either the shape \\`{ data: <value> }\\` or \\`{ error: <value> }\\` that may contain an optional \\`meta\\` property.\n                  Object returned was:`, result);\n          }\n        }\n        if (result.error) throw new HandledError(result.error, result.meta);\n        let {\n          data\n        } = result;\n        if (rawResponseSchema && !skipSchemaValidation) {\n          data = await parseWithSchema(rawResponseSchema, result.data, \"rawResponseSchema\", result.meta);\n        }\n        let transformedResponse = await transformResponse(data, result.meta, finalQueryArg);\n        if (responseSchema && !skipSchemaValidation) {\n          transformedResponse = await parseWithSchema(responseSchema, transformedResponse, \"responseSchema\", result.meta);\n        }\n        return {\n          ...result,\n          data: transformedResponse\n        };\n      }\n      if (arg.type === \"query\" && \"infiniteQueryOptions\" in endpointDefinition) {\n        const {\n          infiniteQueryOptions\n        } = endpointDefinition;\n        const {\n          maxPages = Infinity\n        } = infiniteQueryOptions;\n        let result;\n        const blankData = {\n          pages: [],\n          pageParams: []\n        };\n        const cachedData = selectors.selectQueryEntry(getState(), arg.queryCacheKey)?.data;\n        const isForcedQueryNeedingRefetch = (\n          // arg.forceRefetch\n          isForcedQuery(arg, getState()) && !arg.direction\n        );\n        const existingData = isForcedQueryNeedingRefetch || !cachedData ? blankData : cachedData;\n        if (\"direction\" in arg && arg.direction && existingData.pages.length) {\n          const previous = arg.direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const param = pageParamFn(infiniteQueryOptions, existingData, arg.originalArgs);\n          result = await fetchPage(existingData, param, maxPages, previous);\n        } else {\n          const {\n            initialPageParam = infiniteQueryOptions.initialPageParam\n          } = arg;\n          const cachedPageParams = cachedData?.pageParams ?? [];\n          const firstPageParam = cachedPageParams[0] ?? initialPageParam;\n          const totalPages = cachedPageParams.length;\n          result = await fetchPage(existingData, firstPageParam, maxPages);\n          if (forceQueryFn) {\n            result = {\n              data: result.data.pages[0]\n            };\n          }\n          for (let i = 1; i < totalPages; i++) {\n            const param = getNextPageParam(infiniteQueryOptions, result.data, arg.originalArgs);\n            result = await fetchPage(result.data, param, maxPages);\n          }\n        }\n        finalQueryReturnValue = result;\n      } else {\n        finalQueryReturnValue = await executeRequest(arg.originalArgs);\n      }\n      if (metaSchema && !skipSchemaValidation && finalQueryReturnValue.meta) {\n        finalQueryReturnValue.meta = await parseWithSchema(metaSchema, finalQueryReturnValue.meta, \"metaSchema\", finalQueryReturnValue.meta);\n      }\n      return fulfillWithValue(finalQueryReturnValue.data, addShouldAutoBatch({\n        fulfilledTimeStamp: Date.now(),\n        baseQueryMeta: finalQueryReturnValue.meta\n      }));\n    } catch (error) {\n      let caughtError = error;\n      if (caughtError instanceof HandledError) {\n        let transformErrorResponse = getTransformCallbackForEndpoint(endpointDefinition, \"transformErrorResponse\");\n        const {\n          rawErrorResponseSchema,\n          errorResponseSchema\n        } = endpointDefinition;\n        let {\n          value,\n          meta\n        } = caughtError;\n        try {\n          if (rawErrorResponseSchema && !skipSchemaValidation) {\n            value = await parseWithSchema(rawErrorResponseSchema, value, \"rawErrorResponseSchema\", meta);\n          }\n          if (metaSchema && !skipSchemaValidation) {\n            meta = await parseWithSchema(metaSchema, meta, \"metaSchema\", meta);\n          }\n          let transformedErrorResponse = await transformErrorResponse(value, meta, arg.originalArgs);\n          if (errorResponseSchema && !skipSchemaValidation) {\n            transformedErrorResponse = await parseWithSchema(errorResponseSchema, transformedErrorResponse, \"errorResponseSchema\", meta);\n          }\n          return rejectWithValue(transformedErrorResponse, addShouldAutoBatch({\n            baseQueryMeta: meta\n          }));\n        } catch (e) {\n          caughtError = e;\n        }\n      }\n      try {\n        if (caughtError instanceof NamedSchemaError) {\n          const info = {\n            endpoint: arg.endpointName,\n            arg: arg.originalArgs,\n            type: arg.type,\n            queryCacheKey: arg.type === \"query\" ? arg.queryCacheKey : void 0\n          };\n          endpointDefinition.onSchemaFailure?.(caughtError, info);\n          onSchemaFailure?.(caughtError, info);\n          const {\n            catchSchemaFailure = globalCatchSchemaFailure\n          } = endpointDefinition;\n          if (catchSchemaFailure) {\n            return rejectWithValue(catchSchemaFailure(caughtError, info), addShouldAutoBatch({\n              baseQueryMeta: caughtError._bqMeta\n            }));\n          }\n        }\n      } catch (e) {\n        caughtError = e;\n      }\n      if (typeof process !== \"undefined\" && \"development\" !== \"production\") {\n        console.error(`An unhandled error occurred processing a request for the endpoint \"${arg.endpointName}\".\nIn the case of an unhandled error, no tags will be \"provided\" or \"invalidated\".`, caughtError);\n      } else {\n        console.error(caughtError);\n      }\n      throw caughtError;\n    }\n  };\n  function isForcedQuery(arg, state) {\n    const requestState = selectors.selectQueryEntry(state, arg.queryCacheKey);\n    const baseFetchOnMountOrArgChange = selectors.selectConfig(state).refetchOnMountOrArgChange;\n    const fulfilledVal = requestState?.fulfilledTimeStamp;\n    const refetchVal = arg.forceRefetch ?? (arg.subscribe && baseFetchOnMountOrArgChange);\n    if (refetchVal) {\n      return refetchVal === true || (Number(/* @__PURE__ */ new Date()) - Number(fulfilledVal)) / 1e3 >= refetchVal;\n    }\n    return false;\n  }\n  const createQueryThunk = () => {\n    const generatedQueryThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)(`${reducerPath}/executeQuery`, executeEndpoint, {\n      getPendingMeta({\n        arg\n      }) {\n        const endpointDefinition = endpointDefinitions[arg.endpointName];\n        return addShouldAutoBatch({\n          startedTimeStamp: Date.now(),\n          ...isInfiniteQueryDefinition(endpointDefinition) ? {\n            direction: arg.direction\n          } : {}\n        });\n      },\n      condition(queryThunkArg, {\n        getState\n      }) {\n        const state = getState();\n        const requestState = selectors.selectQueryEntry(state, queryThunkArg.queryCacheKey);\n        const fulfilledVal = requestState?.fulfilledTimeStamp;\n        const currentArg = queryThunkArg.originalArgs;\n        const previousArg = requestState?.originalArgs;\n        const endpointDefinition = endpointDefinitions[queryThunkArg.endpointName];\n        const direction = queryThunkArg.direction;\n        if (isUpsertQuery(queryThunkArg)) {\n          return true;\n        }\n        if (requestState?.status === \"pending\") {\n          return false;\n        }\n        if (isForcedQuery(queryThunkArg, state)) {\n          return true;\n        }\n        if (isQueryDefinition(endpointDefinition) && endpointDefinition?.forceRefetch?.({\n          currentArg,\n          previousArg,\n          endpointState: requestState,\n          state\n        })) {\n          return true;\n        }\n        if (fulfilledVal && !direction) {\n          return false;\n        }\n        return true;\n      },\n      dispatchConditionRejection: true\n    });\n    return generatedQueryThunk;\n  };\n  const queryThunk = createQueryThunk();\n  const infiniteQueryThunk = createQueryThunk();\n  const mutationThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAsyncThunk)(`${reducerPath}/executeMutation`, executeEndpoint, {\n    getPendingMeta() {\n      return addShouldAutoBatch({\n        startedTimeStamp: Date.now()\n      });\n    }\n  });\n  const hasTheForce = (options) => \"force\" in options;\n  const hasMaxAge = (options) => \"ifOlderThan\" in options;\n  const prefetch = (endpointName, arg, options) => (dispatch, getState) => {\n    const force = hasTheForce(options) && options.force;\n    const maxAge = hasMaxAge(options) && options.ifOlderThan;\n    const queryAction = (force2 = true) => {\n      const options2 = {\n        forceRefetch: force2,\n        isPrefetch: true\n      };\n      return api.endpoints[endpointName].initiate(arg, options2);\n    };\n    const latestStateValue = api.endpoints[endpointName].select(arg)(getState());\n    if (force) {\n      dispatch(queryAction());\n    } else if (maxAge) {\n      const lastFulfilledTs = latestStateValue?.fulfilledTimeStamp;\n      if (!lastFulfilledTs) {\n        dispatch(queryAction());\n        return;\n      }\n      const shouldRetrigger = (Number(/* @__PURE__ */ new Date()) - Number(new Date(lastFulfilledTs))) / 1e3 >= maxAge;\n      if (shouldRetrigger) {\n        dispatch(queryAction());\n      }\n    } else {\n      dispatch(queryAction(false));\n    }\n  };\n  function matchesEndpoint(endpointName) {\n    return (action) => action?.meta?.arg?.endpointName === endpointName;\n  }\n  function buildMatchThunkActions(thunk, endpointName) {\n    return {\n      matchPending: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAllOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPending)(thunk), matchesEndpoint(endpointName)),\n      matchFulfilled: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAllOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(thunk), matchesEndpoint(endpointName)),\n      matchRejected: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAllOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejected)(thunk), matchesEndpoint(endpointName))\n    };\n  }\n  return {\n    queryThunk,\n    mutationThunk,\n    infiniteQueryThunk,\n    prefetch,\n    updateQueryData,\n    upsertQueryData,\n    patchQueryData,\n    buildMatchThunkActions\n  };\n}\nfunction getNextPageParam(options, {\n  pages,\n  pageParams\n}, queryArg) {\n  const lastIndex = pages.length - 1;\n  return options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams, queryArg);\n}\nfunction getPreviousPageParam(options, {\n  pages,\n  pageParams\n}, queryArg) {\n  return options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams, queryArg);\n}\nfunction calculateProvidedByThunk(action, type, endpointDefinitions, assertTagType) {\n  return calculateProvidedBy(endpointDefinitions[action.meta.arg.endpointName][type], (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(action) ? action.payload : void 0, (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejectedWithValue)(action) ? action.payload : void 0, action.meta.arg.originalArgs, \"baseQueryMeta\" in action.meta ? action.meta.baseQueryMeta : void 0, assertTagType);\n}\n\n// src/query/core/buildSlice.ts\n\n\nfunction updateQuerySubstateIfExists(state, queryCacheKey, update) {\n  const substate = state[queryCacheKey];\n  if (substate) {\n    update(substate);\n  }\n}\nfunction getMutationCacheKey(id) {\n  return (\"arg\" in id ? id.arg.fixedCacheKey : id.fixedCacheKey) ?? id.requestId;\n}\nfunction updateMutationSubstateIfExists(state, id, update) {\n  const substate = state[getMutationCacheKey(id)];\n  if (substate) {\n    update(substate);\n  }\n}\nvar initialState = {};\nfunction buildSlice({\n  reducerPath,\n  queryThunk,\n  mutationThunk,\n  serializeQueryArgs,\n  context: {\n    endpointDefinitions: definitions,\n    apiUid,\n    extractRehydrationInfo,\n    hasRehydrationInfo\n  },\n  assertTagType,\n  config\n}) {\n  const resetApiState = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(`${reducerPath}/resetApiState`);\n  function writePendingCacheEntry(draft, arg, upserting, meta) {\n    draft[arg.queryCacheKey] ??= {\n      status: \"uninitialized\" /* uninitialized */,\n      endpointName: arg.endpointName\n    };\n    updateQuerySubstateIfExists(draft, arg.queryCacheKey, (substate) => {\n      substate.status = \"pending\" /* pending */;\n      substate.requestId = upserting && substate.requestId ? (\n        // for `upsertQuery` **updates**, keep the current `requestId`\n        substate.requestId\n      ) : (\n        // for normal queries or `upsertQuery` **inserts** always update the `requestId`\n        meta.requestId\n      );\n      if (arg.originalArgs !== void 0) {\n        substate.originalArgs = arg.originalArgs;\n      }\n      substate.startedTimeStamp = meta.startedTimeStamp;\n      const endpointDefinition = definitions[meta.arg.endpointName];\n      if (isInfiniteQueryDefinition(endpointDefinition) && \"direction\" in arg) {\n        ;\n        substate.direction = arg.direction;\n      }\n    });\n  }\n  function writeFulfilledCacheEntry(draft, meta, payload, upserting) {\n    updateQuerySubstateIfExists(draft, meta.arg.queryCacheKey, (substate) => {\n      if (substate.requestId !== meta.requestId && !upserting) return;\n      const {\n        merge\n      } = definitions[meta.arg.endpointName];\n      substate.status = \"fulfilled\" /* fulfilled */;\n      if (merge) {\n        if (substate.data !== void 0) {\n          const {\n            fulfilledTimeStamp,\n            arg,\n            baseQueryMeta,\n            requestId\n          } = meta;\n          let newData = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createNextState)(substate.data, (draftSubstateData) => {\n            return merge(draftSubstateData, payload, {\n              arg: arg.originalArgs,\n              baseQueryMeta,\n              fulfilledTimeStamp,\n              requestId\n            });\n          });\n          substate.data = newData;\n        } else {\n          substate.data = payload;\n        }\n      } else {\n        substate.data = definitions[meta.arg.endpointName].structuralSharing ?? true ? copyWithStructuralSharing((0,immer__WEBPACK_IMPORTED_MODULE_2__.isDraft)(substate.data) ? (0,immer__WEBPACK_IMPORTED_MODULE_2__.original)(substate.data) : substate.data, payload) : payload;\n      }\n      delete substate.error;\n      substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n    });\n  }\n  const querySlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/queries`,\n    initialState,\n    reducers: {\n      removeQueryResult: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey\n          }\n        }) {\n          delete draft[queryCacheKey];\n        },\n        prepare: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.prepareAutoBatched)()\n      },\n      cacheEntriesUpserted: {\n        reducer(draft, action) {\n          for (const entry of action.payload) {\n            const {\n              queryDescription: arg,\n              value\n            } = entry;\n            writePendingCacheEntry(draft, arg, true, {\n              arg,\n              requestId: action.meta.requestId,\n              startedTimeStamp: action.meta.timestamp\n            });\n            writeFulfilledCacheEntry(\n              draft,\n              {\n                arg,\n                requestId: action.meta.requestId,\n                fulfilledTimeStamp: action.meta.timestamp,\n                baseQueryMeta: {}\n              },\n              value,\n              // We know we're upserting here\n              true\n            );\n          }\n        },\n        prepare: (payload) => {\n          const queryDescriptions = payload.map((entry) => {\n            const {\n              endpointName,\n              arg,\n              value\n            } = entry;\n            const endpointDefinition = definitions[endpointName];\n            const queryDescription = {\n              type: \"query\",\n              endpointName,\n              originalArgs: entry.arg,\n              queryCacheKey: serializeQueryArgs({\n                queryArgs: arg,\n                endpointDefinition,\n                endpointName\n              })\n            };\n            return {\n              queryDescription,\n              value\n            };\n          });\n          const result = {\n            payload: queryDescriptions,\n            meta: {\n              [_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.SHOULD_AUTOBATCH]: true,\n              requestId: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.nanoid)(),\n              timestamp: Date.now()\n            }\n          };\n          return result;\n        }\n      },\n      queryResultPatched: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey,\n            patches\n          }\n        }) {\n          updateQuerySubstateIfExists(draft, queryCacheKey, (substate) => {\n            substate.data = (0,immer__WEBPACK_IMPORTED_MODULE_2__.applyPatches)(substate.data, patches.concat());\n          });\n        },\n        prepare: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(queryThunk.pending, (draft, {\n        meta,\n        meta: {\n          arg\n        }\n      }) => {\n        const upserting = isUpsertQuery(arg);\n        writePendingCacheEntry(draft, arg, upserting, meta);\n      }).addCase(queryThunk.fulfilled, (draft, {\n        meta,\n        payload\n      }) => {\n        const upserting = isUpsertQuery(meta.arg);\n        writeFulfilledCacheEntry(draft, meta, payload, upserting);\n      }).addCase(queryThunk.rejected, (draft, {\n        meta: {\n          condition,\n          arg,\n          requestId\n        },\n        error,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, (substate) => {\n          if (condition) {\n          } else {\n            if (substate.requestId !== requestId) return;\n            substate.status = \"rejected\" /* rejected */;\n            substate.error = payload ?? error;\n          }\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          queries\n        } = extractRehydrationInfo(action);\n        for (const [key, entry] of Object.entries(queries)) {\n          if (\n            // do not rehydrate entries that were currently in flight.\n            entry?.status === \"fulfilled\" /* fulfilled */ || entry?.status === \"rejected\" /* rejected */\n          ) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n  });\n  const mutationSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/mutations`,\n    initialState,\n    reducers: {\n      removeMutationResult: {\n        reducer(draft, {\n          payload\n        }) {\n          const cacheKey = getMutationCacheKey(payload);\n          if (cacheKey in draft) {\n            delete draft[cacheKey];\n          }\n        },\n        prepare: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(mutationThunk.pending, (draft, {\n        meta,\n        meta: {\n          requestId,\n          arg,\n          startedTimeStamp\n        }\n      }) => {\n        if (!arg.track) return;\n        draft[getMutationCacheKey(meta)] = {\n          requestId,\n          status: \"pending\" /* pending */,\n          endpointName: arg.endpointName,\n          startedTimeStamp\n        };\n      }).addCase(mutationThunk.fulfilled, (draft, {\n        payload,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, (substate) => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = \"fulfilled\" /* fulfilled */;\n          substate.data = payload;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(mutationThunk.rejected, (draft, {\n        payload,\n        error,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, (substate) => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = \"rejected\" /* rejected */;\n          substate.error = payload ?? error;\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          mutations\n        } = extractRehydrationInfo(action);\n        for (const [key, entry] of Object.entries(mutations)) {\n          if (\n            // do not rehydrate entries that were currently in flight.\n            (entry?.status === \"fulfilled\" /* fulfilled */ || entry?.status === \"rejected\" /* rejected */) && // only rehydrate endpoints that were persisted using a `fixedCacheKey`\n            key !== entry?.requestId\n          ) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n  });\n  const initialInvalidationState = {\n    tags: {},\n    keys: {}\n  };\n  const invalidationSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/invalidation`,\n    initialState: initialInvalidationState,\n    reducers: {\n      updateProvidedBy: {\n        reducer(draft, action) {\n          for (const {\n            queryCacheKey,\n            providedTags\n          } of action.payload) {\n            removeCacheKeyFromTags(draft, queryCacheKey);\n            for (const {\n              type,\n              id\n            } of providedTags) {\n              const subscribedQueries = (draft.tags[type] ??= {})[id || \"__internal_without_id\"] ??= [];\n              const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n              if (!alreadySubscribed) {\n                subscribedQueries.push(queryCacheKey);\n              }\n            }\n            draft.keys[queryCacheKey] = providedTags;\n          }\n        },\n        prepare: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.prepareAutoBatched)()\n      }\n    },\n    extraReducers(builder) {\n      builder.addCase(querySlice.actions.removeQueryResult, (draft, {\n        payload: {\n          queryCacheKey\n        }\n      }) => {\n        removeCacheKeyFromTags(draft, queryCacheKey);\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          provided\n        } = extractRehydrationInfo(action);\n        for (const [type, incomingTags] of Object.entries(provided)) {\n          for (const [id, cacheKeys] of Object.entries(incomingTags)) {\n            const subscribedQueries = (draft.tags[type] ??= {})[id || \"__internal_without_id\"] ??= [];\n            for (const queryCacheKey of cacheKeys) {\n              const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n              if (!alreadySubscribed) {\n                subscribedQueries.push(queryCacheKey);\n              }\n            }\n          }\n        }\n      }).addMatcher((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAnyOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(queryThunk), (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejectedWithValue)(queryThunk)), (draft, action) => {\n        writeProvidedTagsForQueries(draft, [action]);\n      }).addMatcher(querySlice.actions.cacheEntriesUpserted.match, (draft, action) => {\n        const mockActions = action.payload.map(({\n          queryDescription,\n          value\n        }) => {\n          return {\n            type: \"UNKNOWN\",\n            payload: value,\n            meta: {\n              requestStatus: \"fulfilled\",\n              requestId: \"UNKNOWN\",\n              arg: queryDescription\n            }\n          };\n        });\n        writeProvidedTagsForQueries(draft, mockActions);\n      });\n    }\n  });\n  function removeCacheKeyFromTags(draft, queryCacheKey) {\n    const existingTags = draft.keys[queryCacheKey] ?? [];\n    for (const tag of existingTags) {\n      const tagType = tag.type;\n      const tagId = tag.id ?? \"__internal_without_id\";\n      const tagSubscriptions = draft.tags[tagType]?.[tagId];\n      if (tagSubscriptions) {\n        draft.tags[tagType][tagId] = tagSubscriptions.filter((qc) => qc !== queryCacheKey);\n      }\n    }\n    delete draft.keys[queryCacheKey];\n  }\n  function writeProvidedTagsForQueries(draft, actions2) {\n    const providedByEntries = actions2.map((action) => {\n      const providedTags = calculateProvidedByThunk(action, \"providesTags\", definitions, assertTagType);\n      const {\n        queryCacheKey\n      } = action.meta.arg;\n      return {\n        queryCacheKey,\n        providedTags\n      };\n    });\n    invalidationSlice.caseReducers.updateProvidedBy(draft, invalidationSlice.actions.updateProvidedBy(providedByEntries));\n  }\n  const subscriptionSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/subscriptions`,\n    initialState,\n    reducers: {\n      updateSubscriptionOptions(d, a) {\n      },\n      unsubscribeQueryResult(d, a) {\n      },\n      internal_getRTKQSubscriptions() {\n      }\n    }\n  });\n  const internalSubscriptionsSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/internalSubscriptions`,\n    initialState,\n    reducers: {\n      subscriptionsUpdated: {\n        reducer(state, action) {\n          return (0,immer__WEBPACK_IMPORTED_MODULE_2__.applyPatches)(state, action.payload);\n        },\n        prepare: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.prepareAutoBatched)()\n      }\n    }\n  });\n  const configSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: `${reducerPath}/config`,\n    initialState: {\n      online: isOnline(),\n      focused: isDocumentVisible(),\n      middlewareRegistered: false,\n      ...config\n    },\n    reducers: {\n      middlewareRegistered(state, {\n        payload\n      }) {\n        state.middlewareRegistered = state.middlewareRegistered === \"conflict\" || apiUid !== payload ? \"conflict\" : true;\n      }\n    },\n    extraReducers: (builder) => {\n      builder.addCase(onOnline, (state) => {\n        state.online = true;\n      }).addCase(onOffline, (state) => {\n        state.online = false;\n      }).addCase(onFocus, (state) => {\n        state.focused = true;\n      }).addCase(onFocusLost, (state) => {\n        state.focused = false;\n      }).addMatcher(hasRehydrationInfo, (draft) => ({\n        ...draft\n      }));\n    }\n  });\n  const combinedReducer = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.combineReducers)({\n    queries: querySlice.reducer,\n    mutations: mutationSlice.reducer,\n    provided: invalidationSlice.reducer,\n    subscriptions: internalSubscriptionsSlice.reducer,\n    config: configSlice.reducer\n  });\n  const reducer = (state, action) => combinedReducer(resetApiState.match(action) ? void 0 : state, action);\n  const actions = {\n    ...configSlice.actions,\n    ...querySlice.actions,\n    ...subscriptionSlice.actions,\n    ...internalSubscriptionsSlice.actions,\n    ...mutationSlice.actions,\n    ...invalidationSlice.actions,\n    resetApiState\n  };\n  return {\n    reducer,\n    actions\n  };\n}\n\n// src/query/core/buildSelectors.ts\nvar skipToken = /* @__PURE__ */ Symbol.for(\"RTKQ/skipToken\");\nvar initialSubState = {\n  status: \"uninitialized\" /* uninitialized */\n};\nvar defaultQuerySubState = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createNextState)(initialSubState, () => {\n});\nvar defaultMutationSubState = /* @__PURE__ */ (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createNextState)(initialSubState, () => {\n});\nfunction buildSelectors({\n  serializeQueryArgs,\n  reducerPath,\n  createSelector: createSelector2\n}) {\n  const selectSkippedQuery = (state) => defaultQuerySubState;\n  const selectSkippedMutation = (state) => defaultMutationSubState;\n  return {\n    buildQuerySelector,\n    buildInfiniteQuerySelector,\n    buildMutationSelector,\n    selectInvalidatedBy,\n    selectCachedArgsForQuery,\n    selectApiState,\n    selectQueries,\n    selectMutations,\n    selectQueryEntry,\n    selectConfig\n  };\n  function withRequestFlags(substate) {\n    return {\n      ...substate,\n      ...getRequestStatusFlags(substate.status)\n    };\n  }\n  function selectApiState(rootState) {\n    const state = rootState[reducerPath];\n    if (true) {\n      if (!state) {\n        if (selectApiState.triggered) return state;\n        selectApiState.triggered = true;\n        console.error(`Error: No data found at \\`state.${reducerPath}\\`. Did you forget to add the reducer to the store?`);\n      }\n    }\n    return state;\n  }\n  function selectQueries(rootState) {\n    return selectApiState(rootState)?.queries;\n  }\n  function selectQueryEntry(rootState, cacheKey) {\n    return selectQueries(rootState)?.[cacheKey];\n  }\n  function selectMutations(rootState) {\n    return selectApiState(rootState)?.mutations;\n  }\n  function selectConfig(rootState) {\n    return selectApiState(rootState)?.config;\n  }\n  function buildAnyQuerySelector(endpointName, endpointDefinition, combiner) {\n    return (queryArgs) => {\n      if (queryArgs === skipToken) {\n        return createSelector2(selectSkippedQuery, combiner);\n      }\n      const serializedArgs = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      const selectQuerySubstate = (state) => selectQueryEntry(state, serializedArgs) ?? defaultQuerySubState;\n      return createSelector2(selectQuerySubstate, combiner);\n    };\n  }\n  function buildQuerySelector(endpointName, endpointDefinition) {\n    return buildAnyQuerySelector(endpointName, endpointDefinition, withRequestFlags);\n  }\n  function buildInfiniteQuerySelector(endpointName, endpointDefinition) {\n    const {\n      infiniteQueryOptions\n    } = endpointDefinition;\n    function withInfiniteQueryResultFlags(substate) {\n      const stateWithRequestFlags = {\n        ...substate,\n        ...getRequestStatusFlags(substate.status)\n      };\n      const {\n        isLoading,\n        isError,\n        direction\n      } = stateWithRequestFlags;\n      const isForward = direction === \"forward\";\n      const isBackward = direction === \"backward\";\n      return {\n        ...stateWithRequestFlags,\n        hasNextPage: getHasNextPage(infiniteQueryOptions, stateWithRequestFlags.data, stateWithRequestFlags.originalArgs),\n        hasPreviousPage: getHasPreviousPage(infiniteQueryOptions, stateWithRequestFlags.data, stateWithRequestFlags.originalArgs),\n        isFetchingNextPage: isLoading && isForward,\n        isFetchingPreviousPage: isLoading && isBackward,\n        isFetchNextPageError: isError && isForward,\n        isFetchPreviousPageError: isError && isBackward\n      };\n    }\n    return buildAnyQuerySelector(endpointName, endpointDefinition, withInfiniteQueryResultFlags);\n  }\n  function buildMutationSelector() {\n    return (id) => {\n      let mutationId;\n      if (typeof id === \"object\") {\n        mutationId = getMutationCacheKey(id) ?? skipToken;\n      } else {\n        mutationId = id;\n      }\n      const selectMutationSubstate = (state) => selectApiState(state)?.mutations?.[mutationId] ?? defaultMutationSubState;\n      const finalSelectMutationSubstate = mutationId === skipToken ? selectSkippedMutation : selectMutationSubstate;\n      return createSelector2(finalSelectMutationSubstate, withRequestFlags);\n    };\n  }\n  function selectInvalidatedBy(state, tags) {\n    const apiState = state[reducerPath];\n    const toInvalidate = /* @__PURE__ */ new Set();\n    for (const tag of tags.filter(isNotNullish).map(expandTagDescription)) {\n      const provided = apiState.provided.tags[tag.type];\n      if (!provided) {\n        continue;\n      }\n      let invalidateSubscriptions = (tag.id !== void 0 ? (\n        // id given: invalidate all queries that provide this type & id\n        provided[tag.id]\n      ) : (\n        // no id: invalidate all queries that provide this type\n        flatten(Object.values(provided))\n      )) ?? [];\n      for (const invalidate of invalidateSubscriptions) {\n        toInvalidate.add(invalidate);\n      }\n    }\n    return flatten(Array.from(toInvalidate.values()).map((queryCacheKey) => {\n      const querySubState = apiState.queries[queryCacheKey];\n      return querySubState ? [{\n        queryCacheKey,\n        endpointName: querySubState.endpointName,\n        originalArgs: querySubState.originalArgs\n      }] : [];\n    }));\n  }\n  function selectCachedArgsForQuery(state, queryName) {\n    return Object.values(selectQueries(state)).filter((entry) => entry?.endpointName === queryName && entry.status !== \"uninitialized\" /* uninitialized */).map((entry) => entry.originalArgs);\n  }\n  function getHasNextPage(options, data, queryArg) {\n    if (!data) return false;\n    return getNextPageParam(options, data, queryArg) != null;\n  }\n  function getHasPreviousPage(options, data, queryArg) {\n    if (!data || !options.getPreviousPageParam) return false;\n    return getPreviousPageParam(options, data, queryArg) != null;\n  }\n}\n\n// src/query/createApi.ts\n\n\n// src/query/defaultSerializeQueryArgs.ts\nvar cache = WeakMap ? /* @__PURE__ */ new WeakMap() : void 0;\nvar defaultSerializeQueryArgs = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = \"\";\n  const cached = cache?.get(queryArgs);\n  if (typeof cached === \"string\") {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => {\n      value = typeof value === \"bigint\" ? {\n        $bigint: value.toString()\n      } : value;\n      value = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(value) ? Object.keys(value).sort().reduce((acc, key2) => {\n        acc[key2] = value[key2];\n        return acc;\n      }, {}) : value;\n      return value;\n    });\n    if ((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n    serialized = stringified;\n  }\n  return `${endpointName}(${serialized})`;\n};\n\n// src/query/createApi.ts\n\nfunction buildCreateApi(...modules) {\n  return function baseCreateApi(options) {\n    const extractRehydrationInfo = (0,reselect__WEBPACK_IMPORTED_MODULE_3__.weakMapMemoize)((action) => options.extractRehydrationInfo?.(action, {\n      reducerPath: options.reducerPath ?? \"api\"\n    }));\n    const optionsWithDefaults = {\n      reducerPath: \"api\",\n      keepUnusedDataFor: 60,\n      refetchOnMountOrArgChange: false,\n      refetchOnFocus: false,\n      refetchOnReconnect: false,\n      invalidationBehavior: \"delayed\",\n      ...options,\n      extractRehydrationInfo,\n      serializeQueryArgs(queryArgsApi) {\n        let finalSerializeQueryArgs = defaultSerializeQueryArgs;\n        if (\"serializeQueryArgs\" in queryArgsApi.endpointDefinition) {\n          const endpointSQA = queryArgsApi.endpointDefinition.serializeQueryArgs;\n          finalSerializeQueryArgs = (queryArgsApi2) => {\n            const initialResult = endpointSQA(queryArgsApi2);\n            if (typeof initialResult === \"string\") {\n              return initialResult;\n            } else {\n              return defaultSerializeQueryArgs({\n                ...queryArgsApi2,\n                queryArgs: initialResult\n              });\n            }\n          };\n        } else if (options.serializeQueryArgs) {\n          finalSerializeQueryArgs = options.serializeQueryArgs;\n        }\n        return finalSerializeQueryArgs(queryArgsApi);\n      },\n      tagTypes: [...options.tagTypes || []]\n    };\n    const context = {\n      endpointDefinitions: {},\n      batch(fn) {\n        fn();\n      },\n      apiUid: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.nanoid)(),\n      extractRehydrationInfo,\n      hasRehydrationInfo: (0,reselect__WEBPACK_IMPORTED_MODULE_3__.weakMapMemoize)((action) => extractRehydrationInfo(action) != null)\n    };\n    const api = {\n      injectEndpoints,\n      enhanceEndpoints({\n        addTagTypes,\n        endpoints\n      }) {\n        if (addTagTypes) {\n          for (const eT of addTagTypes) {\n            if (!optionsWithDefaults.tagTypes.includes(eT)) {\n              ;\n              optionsWithDefaults.tagTypes.push(eT);\n            }\n          }\n        }\n        if (endpoints) {\n          for (const [endpointName, partialDefinition] of Object.entries(endpoints)) {\n            if (typeof partialDefinition === \"function\") {\n              partialDefinition(context.endpointDefinitions[endpointName]);\n            } else {\n              Object.assign(context.endpointDefinitions[endpointName] || {}, partialDefinition);\n            }\n          }\n        }\n        return api;\n      }\n    };\n    const initializedModules = modules.map((m) => m.init(api, optionsWithDefaults, context));\n    function injectEndpoints(inject) {\n      const evaluatedEndpoints = inject.endpoints({\n        query: (x) => ({\n          ...x,\n          type: \"query\" /* query */\n        }),\n        mutation: (x) => ({\n          ...x,\n          type: \"mutation\" /* mutation */\n        }),\n        infiniteQuery: (x) => ({\n          ...x,\n          type: \"infinitequery\" /* infinitequery */\n        })\n      });\n      for (const [endpointName, definition] of Object.entries(evaluatedEndpoints)) {\n        if (inject.overrideExisting !== true && endpointName in context.endpointDefinitions) {\n          if (inject.overrideExisting === \"throw\") {\n            throw new Error( false ? 0 : `called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          } else if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n            console.error(`called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          }\n          continue;\n        }\n        if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n          if (isInfiniteQueryDefinition(definition)) {\n            const {\n              infiniteQueryOptions\n            } = definition;\n            const {\n              maxPages,\n              getPreviousPageParam: getPreviousPageParam2\n            } = infiniteQueryOptions;\n            if (typeof maxPages === \"number\") {\n              if (maxPages < 1) {\n                throw new Error( false ? 0 : `maxPages for endpoint '${endpointName}' must be a number greater than 0`);\n              }\n              if (typeof getPreviousPageParam2 !== \"function\") {\n                throw new Error( false ? 0 : `getPreviousPageParam for endpoint '${endpointName}' must be a function if maxPages is used`);\n              }\n            }\n          }\n        }\n        context.endpointDefinitions[endpointName] = definition;\n        for (const m of initializedModules) {\n          m.injectEndpoint(endpointName, definition);\n        }\n      }\n      return api;\n    }\n    return api.injectEndpoints({\n      endpoints: options.endpoints\n    });\n  };\n}\n\n// src/query/fakeBaseQuery.ts\n\nvar _NEVER = /* @__PURE__ */ Symbol();\nfunction fakeBaseQuery() {\n  return function() {\n    throw new Error( false ? 0 : \"When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.\");\n  };\n}\n\n// src/query/core/module.ts\n\n\n// src/query/tsHelpers.ts\nfunction assertCast(v) {\n}\nfunction safeAssign(target, ...args) {\n  return Object.assign(target, ...args);\n}\n\n// src/query/core/buildMiddleware/batchActions.ts\n\nvar buildBatchedActionsHandler = ({\n  api,\n  queryThunk,\n  internalState\n}) => {\n  const subscriptionsPrefix = `${api.reducerPath}/subscriptions`;\n  let previousSubscriptions = null;\n  let updateSyncTimer = null;\n  const {\n    updateSubscriptionOptions,\n    unsubscribeQueryResult\n  } = api.internalActions;\n  const actuallyMutateSubscriptions = (mutableState, action) => {\n    if (updateSubscriptionOptions.match(action)) {\n      const {\n        queryCacheKey,\n        requestId,\n        options\n      } = action.payload;\n      if (mutableState?.[queryCacheKey]?.[requestId]) {\n        mutableState[queryCacheKey][requestId] = options;\n      }\n      return true;\n    }\n    if (unsubscribeQueryResult.match(action)) {\n      const {\n        queryCacheKey,\n        requestId\n      } = action.payload;\n      if (mutableState[queryCacheKey]) {\n        delete mutableState[queryCacheKey][requestId];\n      }\n      return true;\n    }\n    if (api.internalActions.removeQueryResult.match(action)) {\n      delete mutableState[action.payload.queryCacheKey];\n      return true;\n    }\n    if (queryThunk.pending.match(action)) {\n      const {\n        meta: {\n          arg,\n          requestId\n        }\n      } = action;\n      const substate = mutableState[arg.queryCacheKey] ??= {};\n      substate[`${requestId}_running`] = {};\n      if (arg.subscribe) {\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n      }\n      return true;\n    }\n    let mutated = false;\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action)) {\n      const state = mutableState[action.meta.arg.queryCacheKey] || {};\n      const key = `${action.meta.requestId}_running`;\n      mutated ||= !!state[key];\n      delete state[key];\n    }\n    if (queryThunk.rejected.match(action)) {\n      const {\n        meta: {\n          condition,\n          arg,\n          requestId\n        }\n      } = action;\n      if (condition && arg.subscribe) {\n        const substate = mutableState[arg.queryCacheKey] ??= {};\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n        mutated = true;\n      }\n    }\n    return mutated;\n  };\n  const getSubscriptions = () => internalState.currentSubscriptions;\n  const getSubscriptionCount = (queryCacheKey) => {\n    const subscriptions = getSubscriptions();\n    const subscriptionsForQueryArg = subscriptions[queryCacheKey] ?? {};\n    return countObjectKeys(subscriptionsForQueryArg);\n  };\n  const isRequestSubscribed = (queryCacheKey, requestId) => {\n    const subscriptions = getSubscriptions();\n    return !!subscriptions?.[queryCacheKey]?.[requestId];\n  };\n  const subscriptionSelectors = {\n    getSubscriptions,\n    getSubscriptionCount,\n    isRequestSubscribed\n  };\n  return (action, mwApi) => {\n    if (!previousSubscriptions) {\n      previousSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n    }\n    if (api.util.resetApiState.match(action)) {\n      previousSubscriptions = internalState.currentSubscriptions = {};\n      updateSyncTimer = null;\n      return [true, false];\n    }\n    if (api.internalActions.internal_getRTKQSubscriptions.match(action)) {\n      return [false, subscriptionSelectors];\n    }\n    const didMutate = actuallyMutateSubscriptions(internalState.currentSubscriptions, action);\n    let actionShouldContinue = true;\n    if (didMutate) {\n      if (!updateSyncTimer) {\n        updateSyncTimer = setTimeout(() => {\n          const newSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n          const [, patches] = (0,immer__WEBPACK_IMPORTED_MODULE_2__.produceWithPatches)(previousSubscriptions, () => newSubscriptions);\n          mwApi.next(api.internalActions.subscriptionsUpdated(patches));\n          previousSubscriptions = newSubscriptions;\n          updateSyncTimer = null;\n        }, 500);\n      }\n      const isSubscriptionSliceAction = typeof action.type == \"string\" && !!action.type.startsWith(subscriptionsPrefix);\n      const isAdditionalSubscriptionAction = queryThunk.rejected.match(action) && action.meta.condition && !!action.meta.arg.subscribe;\n      actionShouldContinue = !isSubscriptionSliceAction && !isAdditionalSubscriptionAction;\n    }\n    return [actionShouldContinue, false];\n  };\n};\n\n// src/query/core/buildMiddleware/cacheCollection.ts\nfunction isObjectEmpty(obj) {\n  for (const k in obj) {\n    return false;\n  }\n  return true;\n}\nvar THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2147483647 / 1e3 - 1;\nvar buildCacheCollectionHandler = ({\n  reducerPath,\n  api,\n  queryThunk,\n  context,\n  internalState,\n  selectors: {\n    selectQueryEntry,\n    selectConfig\n  }\n}) => {\n  const {\n    removeQueryResult,\n    unsubscribeQueryResult,\n    cacheEntriesUpserted\n  } = api.internalActions;\n  const canTriggerUnsubscribe = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAnyOf)(unsubscribeQueryResult.match, queryThunk.fulfilled, queryThunk.rejected, cacheEntriesUpserted.match);\n  function anySubscriptionsRemainingForKey(queryCacheKey) {\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    return !!subscriptions && !isObjectEmpty(subscriptions);\n  }\n  const currentRemovalTimeouts = {};\n  const handler = (action, mwApi, internalState2) => {\n    const state = mwApi.getState();\n    const config = selectConfig(state);\n    if (canTriggerUnsubscribe(action)) {\n      let queryCacheKeys;\n      if (cacheEntriesUpserted.match(action)) {\n        queryCacheKeys = action.payload.map((entry) => entry.queryDescription.queryCacheKey);\n      } else {\n        const {\n          queryCacheKey\n        } = unsubscribeQueryResult.match(action) ? action.payload : action.meta.arg;\n        queryCacheKeys = [queryCacheKey];\n      }\n      handleUnsubscribeMany(queryCacheKeys, mwApi, config);\n    }\n    if (api.util.resetApiState.match(action)) {\n      for (const [key, timeout] of Object.entries(currentRemovalTimeouts)) {\n        if (timeout) clearTimeout(timeout);\n        delete currentRemovalTimeouts[key];\n      }\n    }\n    if (context.hasRehydrationInfo(action)) {\n      const {\n        queries\n      } = context.extractRehydrationInfo(action);\n      handleUnsubscribeMany(Object.keys(queries), mwApi, config);\n    }\n  };\n  function handleUnsubscribeMany(cacheKeys, api2, config) {\n    const state = api2.getState();\n    for (const queryCacheKey of cacheKeys) {\n      const entry = selectQueryEntry(state, queryCacheKey);\n      handleUnsubscribe(queryCacheKey, entry?.endpointName, api2, config);\n    }\n  }\n  function handleUnsubscribe(queryCacheKey, endpointName, api2, config) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const keepUnusedDataFor = endpointDefinition?.keepUnusedDataFor ?? config.keepUnusedDataFor;\n    if (keepUnusedDataFor === Infinity) {\n      return;\n    }\n    const finalKeepUnusedDataFor = Math.max(0, Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS));\n    if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n      const currentTimeout = currentRemovalTimeouts[queryCacheKey];\n      if (currentTimeout) {\n        clearTimeout(currentTimeout);\n      }\n      currentRemovalTimeouts[queryCacheKey] = setTimeout(() => {\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n          api2.dispatch(removeQueryResult({\n            queryCacheKey\n          }));\n        }\n        delete currentRemovalTimeouts[queryCacheKey];\n      }, finalKeepUnusedDataFor * 1e3);\n    }\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/cacheLifecycle.ts\nvar neverResolvedError = new Error(\"Promise never resolved before cacheEntryRemoved.\");\nvar buildCacheLifecycleHandler = ({\n  api,\n  reducerPath,\n  context,\n  queryThunk,\n  mutationThunk,\n  internalState,\n  selectors: {\n    selectQueryEntry,\n    selectApiState\n  }\n}) => {\n  const isQueryThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAsyncThunkAction)(queryThunk);\n  const isMutationThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAsyncThunkAction)(mutationThunk);\n  const isFulfilledThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(queryThunk, mutationThunk);\n  const lifecycleMap = {};\n  function resolveLifecycleEntry(cacheKey, data, meta) {\n    const lifecycle = lifecycleMap[cacheKey];\n    if (lifecycle?.valueResolved) {\n      lifecycle.valueResolved({\n        data,\n        meta\n      });\n      delete lifecycle.valueResolved;\n    }\n  }\n  function removeLifecycleEntry(cacheKey) {\n    const lifecycle = lifecycleMap[cacheKey];\n    if (lifecycle) {\n      delete lifecycleMap[cacheKey];\n      lifecycle.cacheEntryRemoved();\n    }\n  }\n  const handler = (action, mwApi, stateBefore) => {\n    const cacheKey = getCacheKey(action);\n    function checkForNewCacheKey(endpointName, cacheKey2, requestId, originalArgs) {\n      const oldEntry = selectQueryEntry(stateBefore, cacheKey2);\n      const newEntry = selectQueryEntry(mwApi.getState(), cacheKey2);\n      if (!oldEntry && newEntry) {\n        handleNewKey(endpointName, originalArgs, cacheKey2, mwApi, requestId);\n      }\n    }\n    if (queryThunk.pending.match(action)) {\n      checkForNewCacheKey(action.meta.arg.endpointName, cacheKey, action.meta.requestId, action.meta.arg.originalArgs);\n    } else if (api.internalActions.cacheEntriesUpserted.match(action)) {\n      for (const {\n        queryDescription,\n        value\n      } of action.payload) {\n        const {\n          endpointName,\n          originalArgs,\n          queryCacheKey\n        } = queryDescription;\n        checkForNewCacheKey(endpointName, queryCacheKey, action.meta.requestId, originalArgs);\n        resolveLifecycleEntry(queryCacheKey, value, {});\n      }\n    } else if (mutationThunk.pending.match(action)) {\n      const state = mwApi.getState()[reducerPath].mutations[cacheKey];\n      if (state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (isFulfilledThunk(action)) {\n      resolveLifecycleEntry(cacheKey, action.payload, action.meta.baseQueryMeta);\n    } else if (api.internalActions.removeQueryResult.match(action) || api.internalActions.removeMutationResult.match(action)) {\n      removeLifecycleEntry(cacheKey);\n    } else if (api.util.resetApiState.match(action)) {\n      for (const cacheKey2 of Object.keys(lifecycleMap)) {\n        removeLifecycleEntry(cacheKey2);\n      }\n    }\n  };\n  function getCacheKey(action) {\n    if (isQueryThunk(action)) return action.meta.arg.queryCacheKey;\n    if (isMutationThunk(action)) {\n      return action.meta.arg.fixedCacheKey ?? action.meta.requestId;\n    }\n    if (api.internalActions.removeQueryResult.match(action)) return action.payload.queryCacheKey;\n    if (api.internalActions.removeMutationResult.match(action)) return getMutationCacheKey(action.payload);\n    return \"\";\n  }\n  function handleNewKey(endpointName, originalArgs, queryCacheKey, mwApi, requestId) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const onCacheEntryAdded = endpointDefinition?.onCacheEntryAdded;\n    if (!onCacheEntryAdded) return;\n    const lifecycle = {};\n    const cacheEntryRemoved = new Promise((resolve) => {\n      lifecycle.cacheEntryRemoved = resolve;\n    });\n    const cacheDataLoaded = Promise.race([new Promise((resolve) => {\n      lifecycle.valueResolved = resolve;\n    }), cacheEntryRemoved.then(() => {\n      throw neverResolvedError;\n    })]);\n    cacheDataLoaded.catch(() => {\n    });\n    lifecycleMap[queryCacheKey] = lifecycle;\n    const selector = api.endpoints[endpointName].select(isAnyQueryDefinition(endpointDefinition) ? originalArgs : queryCacheKey);\n    const extra = mwApi.dispatch((_, __, extra2) => extra2);\n    const lifecycleApi = {\n      ...mwApi,\n      getCacheEntry: () => selector(mwApi.getState()),\n      requestId,\n      extra,\n      updateCachedData: isAnyQueryDefinition(endpointDefinition) ? (updateRecipe) => mwApi.dispatch(api.util.updateQueryData(endpointName, originalArgs, updateRecipe)) : void 0,\n      cacheDataLoaded,\n      cacheEntryRemoved\n    };\n    const runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi);\n    Promise.resolve(runningHandler).catch((e) => {\n      if (e === neverResolvedError) return;\n      throw e;\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/devMiddleware.ts\nvar buildDevCheckHandler = ({\n  api,\n  context: {\n    apiUid\n  },\n  reducerPath\n}) => {\n  return (action, mwApi) => {\n    if (api.util.resetApiState.match(action)) {\n      mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n    }\n    if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n      if (api.internalActions.middlewareRegistered.match(action) && action.payload === apiUid && mwApi.getState()[reducerPath]?.config?.middlewareRegistered === \"conflict\") {\n        console.warn(`There is a mismatch between slice and middleware for the reducerPath \"${reducerPath}\".\nYou can only have one api per reducer path, this will lead to crashes in various situations!${reducerPath === \"api\" ? `\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!` : \"\"}`);\n      }\n    }\n  };\n};\n\n// src/query/core/buildMiddleware/invalidationByTags.ts\nvar buildInvalidationByTagsHandler = ({\n  reducerPath,\n  context,\n  context: {\n    endpointDefinitions\n  },\n  mutationThunk,\n  queryThunk,\n  api,\n  assertTagType,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const isThunkActionWithTags = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAnyOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(mutationThunk), (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejectedWithValue)(mutationThunk));\n  const isQueryEnd = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAnyOf)((0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(mutationThunk, queryThunk), (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejected)(mutationThunk, queryThunk));\n  let pendingTagInvalidations = [];\n  const handler = (action, mwApi) => {\n    if (isThunkActionWithTags(action)) {\n      invalidateTags(calculateProvidedByThunk(action, \"invalidatesTags\", endpointDefinitions, assertTagType), mwApi);\n    } else if (isQueryEnd(action)) {\n      invalidateTags([], mwApi);\n    } else if (api.util.invalidateTags.match(action)) {\n      invalidateTags(calculateProvidedBy(action.payload, void 0, void 0, void 0, void 0, assertTagType), mwApi);\n    }\n  };\n  function hasPendingRequests(state) {\n    const {\n      queries,\n      mutations\n    } = state;\n    for (const cacheRecord of [queries, mutations]) {\n      for (const key in cacheRecord) {\n        if (cacheRecord[key]?.status === \"pending\" /* pending */) return true;\n      }\n    }\n    return false;\n  }\n  function invalidateTags(newTags, mwApi) {\n    const rootState = mwApi.getState();\n    const state = rootState[reducerPath];\n    pendingTagInvalidations.push(...newTags);\n    if (state.config.invalidationBehavior === \"delayed\" && hasPendingRequests(state)) {\n      return;\n    }\n    const tags = pendingTagInvalidations;\n    pendingTagInvalidations = [];\n    if (tags.length === 0) return;\n    const toInvalidate = api.util.selectInvalidatedBy(rootState, tags);\n    context.batch(() => {\n      const valuesArray = Array.from(toInvalidate.values());\n      for (const {\n        queryCacheKey\n      } of valuesArray) {\n        const querySubState = state.queries[queryCacheKey];\n        const subscriptionSubState = internalState.currentSubscriptions[queryCacheKey] ?? {};\n        if (querySubState) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            mwApi.dispatch(removeQueryResult({\n              queryCacheKey\n            }));\n          } else if (querySubState.status !== \"uninitialized\" /* uninitialized */) {\n            mwApi.dispatch(refetchQuery(querySubState));\n          }\n        }\n      }\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/polling.ts\nvar buildPollingHandler = ({\n  reducerPath,\n  queryThunk,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const currentPolls = {};\n  const handler = (action, mwApi) => {\n    if (api.internalActions.updateSubscriptionOptions.match(action) || api.internalActions.unsubscribeQueryResult.match(action)) {\n      updatePollingInterval(action.payload, mwApi);\n    }\n    if (queryThunk.pending.match(action) || queryThunk.rejected.match(action) && action.meta.condition) {\n      updatePollingInterval(action.meta.arg, mwApi);\n    }\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action) && !action.meta.condition) {\n      startNextPoll(action.meta.arg, mwApi);\n    }\n    if (api.util.resetApiState.match(action)) {\n      clearPolls();\n    }\n  };\n  function getCacheEntrySubscriptions(queryCacheKey, api2) {\n    const state = api2.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === \"uninitialized\" /* uninitialized */) return;\n    return subscriptions;\n  }\n  function startNextPoll({\n    queryCacheKey\n  }, api2) {\n    const state = api2.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === \"uninitialized\" /* uninitialized */) return;\n    const {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    } = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) return;\n    const currentPoll = currentPolls[queryCacheKey];\n    if (currentPoll?.timeout) {\n      clearTimeout(currentPoll.timeout);\n      currentPoll.timeout = void 0;\n    }\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    currentPolls[queryCacheKey] = {\n      nextPollTimestamp,\n      pollingInterval: lowestPollingInterval,\n      timeout: setTimeout(() => {\n        if (state.config.focused || !skipPollingIfUnfocused) {\n          api2.dispatch(refetchQuery(querySubState));\n        }\n        startNextPoll({\n          queryCacheKey\n        }, api2);\n      }, lowestPollingInterval)\n    };\n  }\n  function updatePollingInterval({\n    queryCacheKey\n  }, api2) {\n    const state = api2.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === \"uninitialized\" /* uninitialized */) {\n      return;\n    }\n    const {\n      lowestPollingInterval\n    } = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) {\n      cleanupPollForKey(queryCacheKey);\n      return;\n    }\n    const currentPoll = currentPolls[queryCacheKey];\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\n      startNextPoll({\n        queryCacheKey\n      }, api2);\n    }\n  }\n  function cleanupPollForKey(key) {\n    const existingPoll = currentPolls[key];\n    if (existingPoll?.timeout) {\n      clearTimeout(existingPoll.timeout);\n    }\n    delete currentPolls[key];\n  }\n  function clearPolls() {\n    for (const key of Object.keys(currentPolls)) {\n      cleanupPollForKey(key);\n    }\n  }\n  function findLowestPollingInterval(subscribers = {}) {\n    let skipPollingIfUnfocused = false;\n    let lowestPollingInterval = Number.POSITIVE_INFINITY;\n    for (let key in subscribers) {\n      if (!!subscribers[key].pollingInterval) {\n        lowestPollingInterval = Math.min(subscribers[key].pollingInterval, lowestPollingInterval);\n        skipPollingIfUnfocused = subscribers[key].skipPollingIfUnfocused || skipPollingIfUnfocused;\n      }\n    }\n    return {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    };\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/queryLifecycle.ts\nvar buildQueryLifecycleHandler = ({\n  api,\n  context,\n  queryThunk,\n  mutationThunk\n}) => {\n  const isPendingThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isPending)(queryThunk, mutationThunk);\n  const isRejectedThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isRejected)(queryThunk, mutationThunk);\n  const isFullfilledThunk = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isFulfilled)(queryThunk, mutationThunk);\n  const lifecycleMap = {};\n  const handler = (action, mwApi) => {\n    if (isPendingThunk(action)) {\n      const {\n        requestId,\n        arg: {\n          endpointName,\n          originalArgs\n        }\n      } = action.meta;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const onQueryStarted = endpointDefinition?.onQueryStarted;\n      if (onQueryStarted) {\n        const lifecycle = {};\n        const queryFulfilled = new Promise((resolve, reject) => {\n          lifecycle.resolve = resolve;\n          lifecycle.reject = reject;\n        });\n        queryFulfilled.catch(() => {\n        });\n        lifecycleMap[requestId] = lifecycle;\n        const selector = api.endpoints[endpointName].select(isAnyQueryDefinition(endpointDefinition) ? originalArgs : requestId);\n        const extra = mwApi.dispatch((_, __, extra2) => extra2);\n        const lifecycleApi = {\n          ...mwApi,\n          getCacheEntry: () => selector(mwApi.getState()),\n          requestId,\n          extra,\n          updateCachedData: isAnyQueryDefinition(endpointDefinition) ? (updateRecipe) => mwApi.dispatch(api.util.updateQueryData(endpointName, originalArgs, updateRecipe)) : void 0,\n          queryFulfilled\n        };\n        onQueryStarted(originalArgs, lifecycleApi);\n      }\n    } else if (isFullfilledThunk(action)) {\n      const {\n        requestId,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.resolve({\n        data: action.payload,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    } else if (isRejectedThunk(action)) {\n      const {\n        requestId,\n        rejectedWithValue,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.reject({\n        error: action.payload ?? action.error,\n        isUnhandledError: !rejectedWithValue,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    }\n  };\n  return handler;\n};\n\n// src/query/core/buildMiddleware/windowEventHandling.ts\nvar buildWindowEventHandler = ({\n  reducerPath,\n  context,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const handler = (action, mwApi) => {\n    if (onFocus.match(action)) {\n      refetchValidQueries(mwApi, \"refetchOnFocus\");\n    }\n    if (onOnline.match(action)) {\n      refetchValidQueries(mwApi, \"refetchOnReconnect\");\n    }\n  };\n  function refetchValidQueries(api2, type) {\n    const state = api2.getState()[reducerPath];\n    const queries = state.queries;\n    const subscriptions = internalState.currentSubscriptions;\n    context.batch(() => {\n      for (const queryCacheKey of Object.keys(subscriptions)) {\n        const querySubState = queries[queryCacheKey];\n        const subscriptionSubState = subscriptions[queryCacheKey];\n        if (!subscriptionSubState || !querySubState) continue;\n        const shouldRefetch = Object.values(subscriptionSubState).some((sub) => sub[type] === true) || Object.values(subscriptionSubState).every((sub) => sub[type] === void 0) && state.config[type];\n        if (shouldRefetch) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            api2.dispatch(removeQueryResult({\n              queryCacheKey\n            }));\n          } else if (querySubState.status !== \"uninitialized\" /* uninitialized */) {\n            api2.dispatch(refetchQuery(querySubState));\n          }\n        }\n      }\n    });\n  }\n  return handler;\n};\n\n// src/query/core/buildMiddleware/index.ts\nfunction buildMiddleware(input) {\n  const {\n    reducerPath,\n    queryThunk,\n    api,\n    context\n  } = input;\n  const {\n    apiUid\n  } = context;\n  const actions = {\n    invalidateTags: (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createAction)(`${reducerPath}/invalidateTags`)\n  };\n  const isThisApiSliceAction = (action) => action.type.startsWith(`${reducerPath}/`);\n  const handlerBuilders = [buildDevCheckHandler, buildCacheCollectionHandler, buildInvalidationByTagsHandler, buildPollingHandler, buildCacheLifecycleHandler, buildQueryLifecycleHandler];\n  const middleware = (mwApi) => {\n    let initialized2 = false;\n    const internalState = {\n      currentSubscriptions: {}\n    };\n    const builderArgs = {\n      ...input,\n      internalState,\n      refetchQuery,\n      isThisApiSliceAction\n    };\n    const handlers = handlerBuilders.map((build) => build(builderArgs));\n    const batchedActionsHandler = buildBatchedActionsHandler(builderArgs);\n    const windowEventsHandler = buildWindowEventHandler(builderArgs);\n    return (next) => {\n      return (action) => {\n        if (!(0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {\n          return next(action);\n        }\n        if (!initialized2) {\n          initialized2 = true;\n          mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n        }\n        const mwApiWithNext = {\n          ...mwApi,\n          next\n        };\n        const stateBefore = mwApi.getState();\n        const [actionShouldContinue, internalProbeResult] = batchedActionsHandler(action, mwApiWithNext, stateBefore);\n        let res;\n        if (actionShouldContinue) {\n          res = next(action);\n        } else {\n          res = internalProbeResult;\n        }\n        if (!!mwApi.getState()[reducerPath]) {\n          windowEventsHandler(action, mwApiWithNext, stateBefore);\n          if (isThisApiSliceAction(action) || context.hasRehydrationInfo(action)) {\n            for (const handler of handlers) {\n              handler(action, mwApiWithNext, stateBefore);\n            }\n          }\n        }\n        return res;\n      };\n    };\n  };\n  return {\n    middleware,\n    actions\n  };\n  function refetchQuery(querySubState) {\n    return input.api.endpoints[querySubState.endpointName].initiate(querySubState.originalArgs, {\n      subscribe: false,\n      forceRefetch: true\n    });\n  }\n}\n\n// src/query/core/module.ts\nvar coreModuleName = /* @__PURE__ */ Symbol();\nvar coreModule = ({\n  createSelector: createSelector2 = _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSelector\n} = {}) => ({\n  name: coreModuleName,\n  init(api, {\n    baseQuery,\n    tagTypes,\n    reducerPath,\n    serializeQueryArgs,\n    keepUnusedDataFor,\n    refetchOnMountOrArgChange,\n    refetchOnFocus,\n    refetchOnReconnect,\n    invalidationBehavior,\n    onSchemaFailure,\n    catchSchemaFailure,\n    skipSchemaValidation\n  }, context) {\n    (0,immer__WEBPACK_IMPORTED_MODULE_2__.enablePatches)();\n    assertCast(serializeQueryArgs);\n    const assertTagType = (tag) => {\n      if (typeof process !== \"undefined\" && \"development\" === \"development\") {\n        if (!tagTypes.includes(tag.type)) {\n          console.error(`Tag type '${tag.type}' was used, but not specified in \\`tagTypes\\`!`);\n        }\n      }\n      return tag;\n    };\n    Object.assign(api, {\n      reducerPath,\n      endpoints: {},\n      internalActions: {\n        onOnline,\n        onOffline,\n        onFocus,\n        onFocusLost\n      },\n      util: {}\n    });\n    const selectors = buildSelectors({\n      serializeQueryArgs,\n      reducerPath,\n      createSelector: createSelector2\n    });\n    const {\n      selectInvalidatedBy,\n      selectCachedArgsForQuery,\n      buildQuerySelector,\n      buildInfiniteQuerySelector,\n      buildMutationSelector\n    } = selectors;\n    safeAssign(api.util, {\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    });\n    const {\n      queryThunk,\n      infiniteQueryThunk,\n      mutationThunk,\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      buildMatchThunkActions\n    } = buildThunks({\n      baseQuery,\n      reducerPath,\n      context,\n      api,\n      serializeQueryArgs,\n      assertTagType,\n      selectors,\n      onSchemaFailure,\n      catchSchemaFailure,\n      skipSchemaValidation\n    });\n    const {\n      reducer,\n      actions: sliceActions\n    } = buildSlice({\n      context,\n      queryThunk,\n      infiniteQueryThunk,\n      mutationThunk,\n      serializeQueryArgs,\n      reducerPath,\n      assertTagType,\n      config: {\n        refetchOnFocus,\n        refetchOnReconnect,\n        refetchOnMountOrArgChange,\n        keepUnusedDataFor,\n        reducerPath,\n        invalidationBehavior\n      }\n    });\n    safeAssign(api.util, {\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      resetApiState: sliceActions.resetApiState,\n      upsertQueryEntries: sliceActions.cacheEntriesUpserted\n    });\n    safeAssign(api.internalActions, sliceActions);\n    const {\n      middleware,\n      actions: middlewareActions\n    } = buildMiddleware({\n      reducerPath,\n      context,\n      queryThunk,\n      mutationThunk,\n      infiniteQueryThunk,\n      api,\n      assertTagType,\n      selectors\n    });\n    safeAssign(api.util, middlewareActions);\n    safeAssign(api, {\n      reducer,\n      middleware\n    });\n    const {\n      buildInitiateQuery,\n      buildInitiateInfiniteQuery,\n      buildInitiateMutation,\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueriesThunk,\n      getRunningQueryThunk\n    } = buildInitiate({\n      queryThunk,\n      mutationThunk,\n      infiniteQueryThunk,\n      api,\n      serializeQueryArgs,\n      context\n    });\n    safeAssign(api.util, {\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueryThunk,\n      getRunningQueriesThunk\n    });\n    return {\n      name: coreModuleName,\n      injectEndpoint(endpointName, definition) {\n        const anyApi = api;\n        const endpoint = anyApi.endpoints[endpointName] ??= {};\n        if (isQueryDefinition(definition)) {\n          safeAssign(endpoint, {\n            name: endpointName,\n            select: buildQuerySelector(endpointName, definition),\n            initiate: buildInitiateQuery(endpointName, definition)\n          }, buildMatchThunkActions(queryThunk, endpointName));\n        }\n        if (isMutationDefinition(definition)) {\n          safeAssign(endpoint, {\n            name: endpointName,\n            select: buildMutationSelector(),\n            initiate: buildInitiateMutation(endpointName)\n          }, buildMatchThunkActions(mutationThunk, endpointName));\n        }\n        if (isInfiniteQueryDefinition(definition)) {\n          safeAssign(endpoint, {\n            name: endpointName,\n            select: buildInfiniteQuerySelector(endpointName, definition),\n            initiate: buildInitiateInfiniteQuery(endpointName, definition)\n          }, buildMatchThunkActions(queryThunk, endpointName));\n        }\n      }\n    };\n  }\n});\n\n// src/query/core/index.ts\nvar createApi = /* @__PURE__ */ buildCreateApi(coreModule());\n\n//# sourceMappingURL=rtk-query.modern.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\n");

/***/ }),

/***/ "./node_modules/@standard-schema/utils/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@standard-schema/utils/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchemaError: () => (/* binding */ SchemaError),\n/* harmony export */   getDotPath: () => (/* binding */ getDotPath)\n/* harmony export */ });\n// src/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path?.length) {\n    let dotPath = \"\";\n    for (const item of issue.path) {\n      const key = typeof item === \"object\" ? item.key : item;\n      if (typeof key === \"string\" || typeof key === \"number\") {\n        if (dotPath) {\n          dotPath += `.${key}`;\n        } else {\n          dotPath += key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return dotPath;\n  }\n  return null;\n}\n\n// src/SchemaError/SchemaError.ts\nvar SchemaError = class extends Error {\n  /**\n   * The schema issues.\n   */\n  issues;\n  /**\n   * Creates a schema error with useful information.\n   *\n   * @param issues The schema issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"SchemaError\";\n    this.issues = issues;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN0YW5kYXJkLXNjaGVtYS91dGlscy9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixJQUFJO0FBQzdCLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRSIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL25vZGVfbW9kdWxlcy9Ac3RhbmRhcmQtc2NoZW1hL3V0aWxzL2Rpc3QvaW5kZXguanM/MTYzMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvZ2V0RG90UGF0aC9nZXREb3RQYXRoLnRzXG5mdW5jdGlvbiBnZXREb3RQYXRoKGlzc3VlKSB7XG4gIGlmIChpc3N1ZS5wYXRoPy5sZW5ndGgpIHtcbiAgICBsZXQgZG90UGF0aCA9IFwiXCI7XG4gICAgZm9yIChjb25zdCBpdGVtIG9mIGlzc3VlLnBhdGgpIHtcbiAgICAgIGNvbnN0IGtleSA9IHR5cGVvZiBpdGVtID09PSBcIm9iamVjdFwiID8gaXRlbS5rZXkgOiBpdGVtO1xuICAgICAgaWYgKHR5cGVvZiBrZXkgPT09IFwic3RyaW5nXCIgfHwgdHlwZW9mIGtleSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICBpZiAoZG90UGF0aCkge1xuICAgICAgICAgIGRvdFBhdGggKz0gYC4ke2tleX1gO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGRvdFBhdGggKz0ga2V5O1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRvdFBhdGg7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5cbi8vIHNyYy9TY2hlbWFFcnJvci9TY2hlbWFFcnJvci50c1xudmFyIFNjaGVtYUVycm9yID0gY2xhc3MgZXh0ZW5kcyBFcnJvciB7XG4gIC8qKlxuICAgKiBUaGUgc2NoZW1hIGlzc3Vlcy5cbiAgICovXG4gIGlzc3VlcztcbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBzY2hlbWEgZXJyb3Igd2l0aCB1c2VmdWwgaW5mb3JtYXRpb24uXG4gICAqXG4gICAqIEBwYXJhbSBpc3N1ZXMgVGhlIHNjaGVtYSBpc3N1ZXMuXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihpc3N1ZXMpIHtcbiAgICBzdXBlcihpc3N1ZXNbMF0ubWVzc2FnZSk7XG4gICAgdGhpcy5uYW1lID0gXCJTY2hlbWFFcnJvclwiO1xuICAgIHRoaXMuaXNzdWVzID0gaXNzdWVzO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgU2NoZW1hRXJyb3IsXG4gIGdldERvdFBhdGhcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@standard-schema/utils/dist/index.js\n");

/***/ })

}]);