import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';
import { g as getDefaultExportFromCjs } from './_commonjsHelpers-B85MJLTf.js';
import { r as requireReactDom } from './index-eoEhLOdg.js';
import App from './__federation_expose_App-CsxUJgfG.js';
export { default as routes } from './__federation_expose_Routes-CoSTEVjW.js';
export { a as apiSlice, b as authReducer, d as selectCurrentAccessToken, f as selectCurrentAuthState, c as selectCurrentLoginStatus, e as selectCurrentRefreshToken, s as setCredentials, store, u as unsetCredentials } from './__federation_expose_Store-krOtjX4R.js';
export { default as Home, u as useAppDispatch, a as useAppSelector } from './__federation_expose_Home-Db8wbg1o.js';
export { default as RouterExample } from './__federation_expose_RouterExample-DSDK2k_Y.js';
export { default as MicroFrontendDemo } from './__federation_expose_MicroFrontendDemo-DvRTI6td.js';
export { default as Agents } from './__federation_expose_Agents-h6Zc5M1N.js';
export { default as NewAgents } from './__federation_expose_NewAgent-Dc-UYM85.js';

var client = {};

var hasRequiredClient;

function requireClient () {
	if (hasRequiredClient) return client;
	hasRequiredClient = 1;
	var m = requireReactDom();
	{
	  client.createRoot = m.createRoot;
	  client.hydrateRoot = m.hydrateRoot;
	}
	return client;
}

var clientExports = requireClient();
const ReactDOM = /*@__PURE__*/getDefaultExportFromCjs(clientExports);

await importShared('react');

function SimpleTestComponent() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-5 m-5 border-2 border-blue-500 rounded-lg bg-blue-50", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-blue-600 text-xl font-bold mb-2", children: "Simple Test Component" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-700 mb-2", children: "This component is loaded from the Bot UI micro-frontend." }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-700 mb-2", children: "If you can see this styled with Tailwind, the integration is working!" }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-gray-600 text-sm", children: [
      "Current time: ",
      (/* @__PURE__ */ new Date()).toLocaleTimeString()
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4 p-3 bg-green-100 border border-green-300 rounded", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-green-800 font-medium", children: "✅ Tailwind CSS is working if this box is green!" }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-2 flex space-x-2", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors", children: "Test Button" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors", children: "Another Button" })
    ] })
  ] });
}

const React = await importShared('react');
const isFederated = window.location.search.includes("federated=true");
if (!isFederated) {
  const rootElement = document.getElementById("root");
  if (rootElement) {
    ReactDOM.createRoot(rootElement).render(
      /* @__PURE__ */ jsxRuntimeExports.jsx(React.StrictMode, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(App, {}) })
    );
  }
}

export { App, SimpleTestComponent };
