"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_components_RouterExample_tsx"],{

/***/ "./app/components/RouterExample.tsx":
/*!******************************************!*\
  !*** ./app/components/RouterExample.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RouterExample)\n/* harmony export */ });\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _router_routes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../router/routes */ \"./app/router/routes.ts\");\n\n\n\n/**\n * Example component demonstrating how to use the router configuration in a React component\n */\nfunction RouterExample() {\n  // Example agent ID and department\n  var agentId = \"123\";\n  var department = \"sales\";\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"p-4\"\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    className: \"text-2xl font-bold mb-4\"\n  }, \"Router Example\"), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"Static Routes\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6 mb-4\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].HOME\n  }, \"Home\"), \" - \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].HOME), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENTS\n  }, \"Agents\"), \" - \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENTS), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].NEW_AGENT\n  }, \"New Agent\"), \" - \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].NEW_AGENT)), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"Dynamic Routes with Parameters\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6 mb-4\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID(agentId)\n  }, \"Agent \", agentId), \" -\", \" \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID(agentId)), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID_AND_DEPARTMENT(agentId, department)\n  }, \"Agent \", agentId, \" in \", department), \" \", \"- \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID_AND_DEPARTMENT(agentId, department)), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE.invoke(\"456\")\n  }, \"User Profile (with invoke)\"), \" \", \"- \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE.invoke(\"456\")), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL.invoke(\"electronics\", \"789\")\n  }, \"Product Detail (with invoke)\"), \" \", \"- \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL.invoke(\"electronics\", \"789\"))), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"Dynamic Routes as Raw Patterns\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6 mb-4\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE\n  }, \"User Profile (raw pattern)\"), \" - \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE), /*#__PURE__*/React.createElement(\"li\", null, /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Link, {\n    to: _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL\n  }, \"Product Detail (raw pattern)\"), \" - \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL)), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"Direct Access to Route Patterns\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6 mb-4\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, \"AGENT_WITH_ID direct access: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID), /*#__PURE__*/React.createElement(\"li\", null, \"AGENT_WITH_ID_AND_DEPARTMENT direct access:\", \" \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID_AND_DEPARTMENT), /*#__PURE__*/React.createElement(\"li\", null, \"USER_PROFILE direct access: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE), /*#__PURE__*/React.createElement(\"li\", null, \"PRODUCT_DETAIL direct access: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL)), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"Route Patterns via .pattern Property\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6 mb-4\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, \"AGENT_WITH_ID pattern: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID.pattern), /*#__PURE__*/React.createElement(\"li\", null, \"AGENT_WITH_ID_AND_DEPARTMENT pattern:\", \" \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].AGENT_WITH_ID_AND_DEPARTMENT.pattern), /*#__PURE__*/React.createElement(\"li\", null, \"USER_PROFILE pattern: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE.pattern), /*#__PURE__*/React.createElement(\"li\", null, \"PRODUCT_DETAIL pattern: \", _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL.pattern)), /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-xl font-semibold mt-4 mb-2\"\n  }, \"String Operations with Routes\"), /*#__PURE__*/React.createElement(\"ul\", {\n    className: \"list-disc pl-6\"\n  }, /*#__PURE__*/React.createElement(\"li\", null, \"String concatenation: \", \"Path: \" + _router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].USER_PROFILE), /*#__PURE__*/React.createElement(\"li\", null, \"String interpolation: \", \"Template: \".concat(_router_routes__WEBPACK_IMPORTED_MODULE_1__[\"default\"].PRODUCT_DETAIL))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/RouterExample.tsx\n");

/***/ }),

/***/ "./app/router/routes.ts":
/*!******************************!*\
  !*** ./app/router/routes.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\n/**\n * Router configuration with support for both static and dynamic routes.\n *\n * Features:\n * - Access static routes directly via dot notation (e.g., routes.HOME)\n * - Access dynamic route patterns directly (e.g., routes.AGENT_WITH_ID returns \"/agent/[id]\")\n * - Generate URLs for dynamic routes by passing parameters:\n *   - Function call: routes.AGENT_WITH_ID(id, department)\n *   - Method invocation: routes.AGENT_WITH_ID.invoke(id, department)\n */\n\n// Type for route parameters\n\n// Type for named route parameters (removed - using ParamsObject instead)\n\n/**\n * Generic interface for a dynamic route that can be both called as a function\n * and accessed directly as a string (returning the pattern)\n *\n * @template P - The string literal type for the pattern\n * @template ParamNames - Tuple type of parameter names\n */\n\n/**\n * Type that makes a dynamic route directly assignable to a string\n * This is the key to making dynamic routes work with React Router's Link component\n * and other contexts that expect string types\n */\n\n/**\n * Helper type to extract parameter names from a route pattern\n *\n * @template P - The string literal type for the pattern\n */\n\n/**\n * Helper type to create a tuple of the same type with a specific length\n *\n * @template T - The type to repeat\n * @template N - The length of the tuple\n * @template Acc - Accumulator for the recursive type\n */\n\n// ParamTuple type removed - using Tuple<RouteParams, N> directly\n\n/**\n * Helper type to create a strongly-typed parameter object from parameter names\n *\n * @template Names - Tuple of parameter names\n */\n\n/**\n * Type for the router constant object with strongly typed routes\n */\n\n/**\n * Creates a dynamic route with parameter substitution capabilities\n * that can also be used directly as a string\n *\n * @param pattern The route pattern with parameters in [param] format\n * @returns A function that can be called with parameters to generate a URL\n *          or accessed directly as a string to get the pattern\n */\nfunction createDynamicRoute(pattern) {\n  var _pattern$match;\n  // Extract parameter names from the pattern\n  var paramNames = ((_pattern$match = pattern.match(/\\[([^\\]]+)\\]/g)) === null || _pattern$match === void 0 ? void 0 : _pattern$match.map(function (param) {\n    return param.substring(1, param.length - 1);\n  })) || [];\n\n  /**\n   * Helper function to generate a URL by replacing parameters in the pattern\n   */\n  var generateUrl = function generateUrl(params) {\n    if (params.length !== paramNames.length) {\n      throw new Error(\"Route \\\"\".concat(pattern, \"\\\" requires \").concat(paramNames.length, \" parameters, but \").concat(params.length, \" were provided.\"));\n    }\n\n    // Replace each parameter in the pattern\n    var result = pattern;\n    for (var i = 0; i < paramNames.length; i++) {\n      result = result.replace(\"[\".concat(paramNames[i], \"]\"), String(params[i]));\n    }\n    return result;\n  };\n\n  // Create the route function\n  var routeFunction = function routeFunction() {\n    for (var _len = arguments.length, params = new Array(_len), _key = 0; _key < _len; _key++) {\n      params[_key] = arguments[_key];\n    }\n    // If no parameters are provided, return the pattern\n    if (params.length === 0 && paramNames.length > 0) {\n      throw new Error(\"Route \\\"\".concat(pattern, \"\\\" requires \").concat(paramNames.length, \" parameters, but 0 were provided.\"));\n    }\n    return generateUrl(params);\n  };\n\n  // Add toString method to return the pattern when used as a string\n  Object.defineProperty(routeFunction, \"toString\", {\n    value: function value() {\n      return pattern;\n    },\n    writable: false,\n    enumerable: false\n  });\n\n  // Add the pattern property\n  Object.defineProperty(routeFunction, \"pattern\", {\n    value: pattern,\n    writable: false,\n    enumerable: true\n  });\n\n  // Add the paramNames property\n  Object.defineProperty(routeFunction, \"paramNames\", {\n    value: Object.freeze(_toConsumableArray(paramNames)),\n    writable: false,\n    enumerable: true\n  });\n\n  // Add the invoke method (positional parameters)\n  Object.defineProperty(routeFunction, \"invoke\", {\n    value: function value() {\n      for (var _len2 = arguments.length, params = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        params[_key2] = arguments[_key2];\n      }\n      // If no parameters are provided and the route has parameters, return the pattern\n      if (params.length === 0) {\n        if (paramNames.length === 0) {\n          return pattern;\n        }\n        throw new Error(\"Route \\\"\".concat(pattern, \"\\\" requires \").concat(paramNames.length, \" parameters, but 0 were provided.\"));\n      }\n      return generateUrl(params);\n    },\n    writable: false,\n    enumerable: true\n  });\n\n  // Add the withParams method (named parameters)\n  Object.defineProperty(routeFunction, \"withParams\", {\n    value: function value(params) {\n      // Check for extra parameters\n      var extraParams = Object.keys(params).filter(function (key) {\n        return !paramNames.includes(key);\n      });\n      if (extraParams.length > 0) {\n        throw new Error(\"Unknown parameter(s) for route \\\"\".concat(pattern, \"\\\": \").concat(extraParams.join(\", \")));\n      }\n\n      // Convert named parameters to positional parameters\n      var positionalParams = paramNames.map(function (name) {\n        if (!(name in params)) {\n          throw new Error(\"Missing parameter \\\"\".concat(name, \"\\\" for route \\\"\").concat(pattern, \"\\\"\"));\n        }\n        return params[name];\n      });\n      return generateUrl(positionalParams);\n    },\n    writable: false,\n    enumerable: true\n  });\n\n  // Make the function work with string concatenation and interpolation\n  Object.defineProperty(routeFunction, Symbol.toPrimitive, {\n    value: function value(_hint) {\n      return pattern;\n    },\n    writable: false,\n    enumerable: false\n  });\n  return routeFunction;\n}\n\n/**\n * Router constant object with both static and dynamic routes\n */\nvar routes = {\n  // Static routes\n  HOME: \"/\",\n  AGENTS: \"/agents\",\n  NEW_AGENT: \"/agents/new\",\n  // Dynamic routes\n  AGENT_WITH_ID: createDynamicRoute(\"/agents/[id]\"),\n  AGENT_WITH_ID_AND_DEPARTMENT: createDynamicRoute(\"/agents/[id]/[department]\"),\n  USER_PROFILE: createDynamicRoute(\"/users/[userId]/profile\"),\n  PRODUCT_DETAIL: createDynamicRoute(\"/products/[category]/[productId]\")\n};\n\n// Export the routes with proper typing\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (routes);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/router/routes.ts\n");

/***/ })

}]);