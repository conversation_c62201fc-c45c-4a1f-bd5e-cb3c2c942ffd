import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';
import { s as setSearchQuery, a as setStatusFilter, d as deleteChatbot } from './chatbotsSlice-BEMjNZK_.js';

const {useDispatch,useSelector} = await importShared('react-redux');

const useAppDispatch = useDispatch;
const useAppSelector = useSelector;

await importShared('react');

const {Link: Link$1,useLocation} = await importShared('react-router-dom');

const Sidebar = ({ collapsed = false }) => {
  const location = useLocation();
  const menuItems = [
    { icon: "🏠", label: "Home", path: "/", id: "home" },
    { icon: "📊", label: "Insights", path: "/insights", id: "insights" },
    { icon: "#", label: "Assets", path: "/assets", id: "assets" },
    { icon: "🔍", label: "Reach", path: "/reach", id: "reach" },
    { icon: "📁", label: "Directory", path: "/directory", id: "directory" },
    { icon: "🎨", label: "Studio", path: "/studio", id: "studio" },
    { icon: "💬", label: "Halo", path: "/halo", id: "halo" },
    {
      icon: "🤖",
      label: "NeuraTalk AI",
      path: "/",
      id: "neuratalk",
      active: true
    },
    { icon: "📋", label: "Plans", path: "/plans", id: "plans" },
    { icon: "📝", label: "Logs", path: "/logs", id: "logs" },
    { icon: "👥", label: "Developers", path: "/developers", id: "developers" }
  ];
  const isActive = (path) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(path);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: `bg-[#2D3748] text-white h-screen flex flex-col transition-all duration-300 ${collapsed ? "w-16" : "w-64"}`,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-[#4A5568]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-white rounded flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-[#2D3748] font-bold text-sm", children: "C" }) }),
          !collapsed && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xl font-bold", children: "comviva" })
        ] }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-[#4A5568]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-8 h-8 bg-[#4A5568] rounded-full flex items-center justify-center relative", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm text-white", children: "JD" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-white text-xs", children: "1" }) })
          ] }),
          !collapsed && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm font-medium text-white", children: "John Doe" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-300", children: "Bot $ 10K" })
          ] })
        ] }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("nav", { className: "flex-1 py-4", children: /* @__PURE__ */ jsxRuntimeExports.jsx("ul", { className: "space-y-1", children: menuItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx("li", { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
          Link$1,
          {
            to: item.path,
            className: `flex items-center px-4 py-3 text-sm transition-colors duration-200 ${item.active || isActive(item.path) ? "bg-[#EF4444] text-white border-r-4 border-[#DC2626]" : "text-gray-300 hover:bg-[#4A5568] hover:text-white"}`,
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-lg mr-3", children: item.icon }),
              !collapsed && /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: item.label })
            ]
          }
        ) }, item.id)) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-t border-[#4A5568]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-6 h-6 bg-[#4A5568] rounded flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs text-white", children: "C+" }) }),
          !collapsed && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-300", children: "Logout" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-400", children: "2023 © Comviva Technologies Limited" })
          ] })
        ] }) })
      ]
    }
  );
};

await importShared('react');

const Header = ({
  title = "NeuraTalk AI",
  subtitle = "NeuraTalk AI is a cutting-edge conversational AI solution designed to enhance customer engagement, automate support, and streamline business operations.",
  actions
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-white border-b border-[#e2e8f0] px-6 py-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start justify-between", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start space-x-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-2xl font-bold text-[#1e293b] mb-2", children: title }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-[#64748b] leading-relaxed max-w-3xl", children: subtitle })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-shrink-0", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center shadow-sm", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-12 h-12 bg-white rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "w-5 h-5 text-white",
          fill: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21ZM14 15.5L22.5 7L21 5.5L14 12.5L10.5 9L9 10.5L14 15.5Z" })
        }
      ) }) }) }) })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-4 ml-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 px-2 py-1 bg-[#f8fafc] rounded-md", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm", children: "🇺🇸" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs font-medium text-[#64748b]", children: "EN" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center space-x-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-[#e2e8f0] rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "w-4 h-4 text-[#64748b]",
          fill: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" })
        }
      ) }) }),
      actions
    ] })
  ] }) });
};

await importShared('react');
const Layout = ({
  children,
  title,
  subtitle,
  headerActions,
  sidebarCollapsed = false
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex h-screen bg-white", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(Sidebar, { collapsed: sidebarCollapsed }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 flex flex-col overflow-hidden", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Header, { title, subtitle, actions: headerActions }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("main", { className: "flex-1 overflow-auto", children })
    ] })
  ] });
};

await importShared('react');
const SearchAndFilters = () => {
  const dispatch = useAppDispatch();
  const { searchQuery, statusFilter } = useAppSelector(
    (state) => state.chatbots
  );
  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value));
  };
  const handleStatusFilterChange = (status) => {
    dispatch(setStatusFilter(status));
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between mb-6", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 max-w-[320px]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "h-4 w-4 text-[#94a3b8]",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            }
          )
        }
      ) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "text",
          placeholder: "Search",
          value: searchQuery,
          onChange: handleSearchChange,
          className: "block w-full pl-9 pr-4 py-2.5 border border-[#e2e8f0] rounded-[8px] leading-5 bg-white placeholder-[#94a3b8] focus:outline-none focus:placeholder-[#64748b] focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] text-sm"
        }
      )
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("select", { className: "appearance-none bg-white border border-[#e2e8f0] rounded-[8px] px-4 py-2.5 pr-10 text-sm font-medium focus:outline-none focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] shadow-sm", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("option", { children: "Last 30 Days" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("option", { children: "Last 7 Days" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("option", { children: "Last 24 Hours" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("option", { children: "All Time" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4 text-[#94a3b8]",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M19 9l-7 7-7-7"
              }
            )
          }
        ) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 bg-gray-50 rounded-xl p-1", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => handleStatusFilterChange("all"),
            className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${statusFilter === "all" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900 hover:bg-white/50"}`,
            children: "All"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => handleStatusFilterChange("live"),
            className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${statusFilter === "live" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900 hover:bg-white/50"}`,
            children: "Live"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => handleStatusFilterChange("draft"),
            className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${statusFilter === "draft" ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:text-gray-900 hover:bg-white/50"}`,
            children: "Draft"
          }
        )
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("button", { className: "flex items-center space-x-2 px-4 py-2.5 border border-[#e2e8f0] rounded-[8px] text-sm font-medium text-[#64748b] hover:bg-[#f8fafc] transition-all duration-200 shadow-sm", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
              }
            )
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "Filter" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center border border-[#e2e8f0] rounded-[8px] overflow-hidden shadow-sm", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2.5 text-[#64748b] hover:text-[#1e293b] bg-white hover:bg-[#f8fafc] transition-all duration-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
              }
            )
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2.5 text-[#94a3b8] hover:text-[#64748b] bg-[#f8fafc] hover:bg-[#f1f5f9] transition-all duration-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M4 6h16M4 10h16M4 14h16M4 18h16"
              }
            )
          }
        ) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "bg-[#EF4444] hover:bg-[#DC2626] text-white px-6 py-2.5 rounded-[8px] text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105", children: "CREATE" })
    ] })
  ] });
};

await importShared('react');

const {Link} = await importShared('react-router-dom');
const ChatbotCard = ({
  chatbot,
  onEdit,
  onDelete
}) => {
  const statusConfig = chatbot.status === "live" ? {
    bgColor: "bg-emerald-500",
    textColor: "text-white",
    dotColor: "bg-white",
    text: "Live"
  } : {
    bgColor: "bg-gray-400",
    textColor: "text-white",
    dotColor: "bg-white",
    text: "Draft"
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white rounded-[12px] border border-[#e2e8f0] p-4 hover:shadow-[0_8px_25px_rgba(0,0,0,0.1)] hover:border-[#cbd5e1] transition-all duration-200 group", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start justify-between mb-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-10 h-10 bg-[#f1f5f9] rounded-[8px] flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-7 h-7 bg-[#64748b] rounded-[5px] flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-4 h-4 bg-white rounded-[2px] flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-1.5 h-1.5 bg-[#64748b] rounded-full" }) }) }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "div",
        {
          className: `px-3 py-1 rounded-full text-xs font-medium ${statusConfig.bgColor} ${statusConfig.textColor}`,
          children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-1", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              "div",
              {
                className: `w-1.5 h-1.5 ${statusConfig.dotColor} rounded-full`
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: statusConfig.text })
          ] })
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-4", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-base font-semibold text-[#1e293b] mb-1.5 group-hover:text-[#0f172a] transition-colors", children: chatbot.name }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-[#64748b] leading-relaxed line-clamp-2", children: chatbot.description })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between pt-3 border-t border-[#f1f5f9]", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-[#94a3b8] font-medium", children: [
        "Last updated ",
        chatbot.lastUpdated
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-1", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          Link,
          {
            to: `/builder/${chatbot.id}`,
            className: "p-1.5 text-[#94a3b8] hover:text-[#3b82f6] hover:bg-[#eff6ff] rounded-md transition-all duration-200",
            title: "Edit",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  }
                )
              }
            )
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: () => onDelete?.(chatbot.id),
            className: "p-1.5 text-[#94a3b8] hover:text-[#ef4444] hover:bg-[#fef2f2] rounded-md transition-all duration-200",
            title: "Delete",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  }
                )
              }
            )
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-1.5 text-[#94a3b8] hover:text-[#64748b] hover:bg-[#f8fafc] rounded-md transition-all duration-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
              }
            )
          }
        ) })
      ] })
    ] })
  ] });
};

await importShared('react');
const ChatbotGrid = () => {
  const dispatch = useAppDispatch();
  const { chatbots, searchQuery, statusFilter, loading } = useAppSelector(
    (state) => state.chatbots
  );
  const filteredChatbots = chatbots.filter((chatbot) => {
    const matchesSearch = chatbot.name.toLowerCase().includes(searchQuery.toLowerCase()) || chatbot.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || chatbot.status === statusFilter;
    return matchesSearch && matchesStatus;
  });
  const handleDelete = (id) => {
    if (window.confirm("Are you sure you want to delete this chatbot?")) {
      dispatch(deleteChatbot(id));
    }
  };
  if (loading) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-center h-64", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-red-600" }) });
  }
  if (filteredChatbots.length === 0) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "w-8 h-8 text-gray-400",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            }
          )
        }
      ) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "No chatbots found" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500 mb-4", children: searchQuery || statusFilter !== "all" ? "Try adjusting your search or filters to find what you're looking for." : "Get started by creating your first chatbot." }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors", children: "Create Chatbot" })
    ] });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5", children: filteredChatbots.map((chatbot) => /* @__PURE__ */ jsxRuntimeExports.jsx(
    ChatbotCard,
    {
      chatbot,
      onDelete: handleDelete
    },
    chatbot.id
  )) });
};

await importShared('react');
function Home() {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Layout, { children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "min-h-screen bg-[#fafbfc] p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "max-w-[1400px] mx-auto", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mb-8 p-6 bg-white rounded-lg shadow-md border", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-2xl font-bold text-gray-800 mb-4", children: "🎨 Tailwind CSS Status" }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 bg-green-100 border border-green-300 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-green-800", children: "✅ Colors Working" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-green-600 text-sm", children: "If you see green styling, colors are working!" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 bg-blue-100 border border-blue-300 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-blue-800", children: "✅ Layout Working" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-blue-600 text-sm", children: "Grid and spacing utilities are functional!" })
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 bg-purple-100 border border-purple-300 rounded-lg", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "font-semibold text-purple-800", children: "✅ Components Working" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-purple-600 text-sm", children: "Rounded corners and shadows are applied!" })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "mt-4 flex space-x-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-200", children: "Primary Button" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors duration-200", children: "Secondary Button" })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(SearchAndFilters, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-6", children: /* @__PURE__ */ jsxRuntimeExports.jsx(ChatbotGrid, {}) })
  ] }) }) });
}

export { Layout as L, useAppSelector as a, Home as default, useAppDispatch as u };
