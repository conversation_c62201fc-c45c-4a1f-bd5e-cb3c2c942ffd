"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_routes_Agents_index_tsx"],{

/***/ "./app/routes/Agents/index.tsx":
/*!*************************************!*\
  !*** ./app/routes/Agents/index.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Agents)\n/* harmony export */ });\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Agents() {\n  return /*#__PURE__*/React.createElement(\"main\", {\n    className: \"flex items-center justify-center pt-16 pb-4\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex-1 flex flex-col items-center gap-16 min-h-0\"\n  }, /*#__PURE__*/React.createElement(\"header\", {\n    className: \"flex flex-col items-center gap-9\"\n  }, \"Agents\"), /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Outlet, null)));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm91dGVzL0FnZW50cy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDO0FBRTNCLFNBQVNDLE1BQU1BLENBQUEsRUFBRztFQUMvQixvQkFDRUMsS0FBQSxDQUFBQyxhQUFBO0lBQU1DLFNBQVMsRUFBQztFQUE2QyxnQkFDM0RGLEtBQUEsQ0FBQUMsYUFBQTtJQUFLQyxTQUFTLEVBQUM7RUFBa0QsZ0JBQy9ERixLQUFBLENBQUFDLGFBQUE7SUFBUUMsU0FBUyxFQUFDO0VBQWtDLEdBQUMsUUFBYyxDQUFDLGVBRXBFRixLQUFBLENBQUFDLGFBQUEsQ0FBQ0gsb0RBQU0sTUFBRSxDQUNOLENBQ0QsQ0FBQztBQUVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3JvdXRlcy9BZ2VudHMvaW5kZXgudHN4P2NjYjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgT3V0bGV0IH0gZnJvbSBcInJlYWN0LXJvdXRlci1kb21cIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWdlbnRzKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB0LTE2IHBiLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0xNiBtaW4taC0wXCI+XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTlcIj5BZ2VudHM8L2hlYWRlcj5cblxuICAgICAgICA8T3V0bGV0IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiT3V0bGV0IiwiQWdlbnRzIiwiUmVhY3QiLCJjcmVhdGVFbGVtZW50IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/routes/Agents/index.tsx\n");

/***/ })

}]);