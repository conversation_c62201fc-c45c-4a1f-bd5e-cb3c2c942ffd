"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_redux_chatbots_chatbotsSlice_ts"],{

/***/ "./app/redux/chatbots/chatbotsSlice.ts":
/*!*********************************************!*\
  !*** ./app/redux/chatbots/chatbotsSlice.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addChatbot: () => (/* binding */ addChatbot),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deleteChatbot: () => (/* binding */ deleteChatbot),\n/* harmony export */   setChatbots: () => (/* binding */ setChatbots),\n/* harmony export */   setError: () => (/* binding */ setError),\n/* harmony export */   setLoading: () => (/* binding */ setLoading),\n/* harmony export */   setSearchQuery: () => (/* binding */ setSearchQuery),\n/* harmony export */   setSelectedChatbot: () => (/* binding */ setSelectedChatbot),\n/* harmony export */   setStatusFilter: () => (/* binding */ setStatusFilter),\n/* harmony export */   updateChatbot: () => (/* binding */ updateChatbot)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"webpack/sharing/consume/default/@reduxjs/toolkit/@reduxjs/toolkit\");\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__);\n\nvar initialState = {\n  chatbots: [{\n    id: \"1\",\n    name: \"Customer Support Bot\",\n    description: \"Automated customer support chatbot for handling common inquiries and support tickets.\",\n    status: \"live\",\n    lastUpdated: \"2 days ago\",\n    createdAt: \"2024-01-15\",\n    flows: []\n  }, {\n    id: \"2\",\n    name: \"Sales Assistant\",\n    description: \"Lead qualification and sales support bot to help convert prospects into customers.\",\n    status: \"draft\",\n    lastUpdated: \"5 days ago\",\n    createdAt: \"2024-01-10\",\n    flows: []\n  }, {\n    id: \"3\",\n    name: \"Product Recommendation\",\n    description: \"AI-powered product recommendation engine to help customers find the right products.\",\n    status: \"live\",\n    lastUpdated: \"2 days ago\",\n    createdAt: \"2024-01-12\",\n    flows: []\n  }, {\n    id: \"4\",\n    name: \"Onboarding Assistant\",\n    description: \"Guide new users through the onboarding process with interactive tutorials.\",\n    status: \"live\",\n    lastUpdated: \"3 days ago\",\n    createdAt: \"2024-01-08\",\n    flows: []\n  }, {\n    id: \"5\",\n    name: \"FAQ Bot\",\n    description: \"Frequently asked questions bot with intelligent search and instant answers.\",\n    status: \"draft\",\n    lastUpdated: \"1 day ago\",\n    createdAt: \"2024-01-20\",\n    flows: []\n  }, {\n    id: \"6\",\n    name: \"Booking Assistant\",\n    description: \"Appointment scheduling and booking management chatbot for service businesses.\",\n    status: \"live\",\n    lastUpdated: \"4 days ago\",\n    createdAt: \"2024-01-05\",\n    flows: []\n  }, {\n    id: \"7\",\n    name: \"Feedback Collector\",\n    description: \"Customer feedback and survey collection bot with sentiment analysis.\",\n    status: \"draft\",\n    lastUpdated: \"6 days ago\",\n    createdAt: \"2024-01-03\",\n    flows: []\n  }, {\n    id: \"8\",\n    name: \"Order Tracker\",\n    description: \"Real-time order tracking and delivery status updates for e-commerce customers.\",\n    status: \"live\",\n    lastUpdated: \"1 week ago\",\n    createdAt: \"2024-01-01\",\n    flows: []\n  }],\n  selectedChatbot: null,\n  loading: false,\n  error: null,\n  searchQuery: \"\",\n  statusFilter: \"all\"\n};\nvar chatbotsSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n  name: \"chatbots\",\n  initialState: initialState,\n  reducers: {\n    setChatbots: function setChatbots(state, action) {\n      state.chatbots = action.payload;\n    },\n    addChatbot: function addChatbot(state, action) {\n      state.chatbots.push(action.payload);\n    },\n    updateChatbot: function updateChatbot(state, action) {\n      var index = state.chatbots.findIndex(function (bot) {\n        return bot.id === action.payload.id;\n      });\n      if (index !== -1) {\n        state.chatbots[index] = action.payload;\n      }\n    },\n    deleteChatbot: function deleteChatbot(state, action) {\n      state.chatbots = state.chatbots.filter(function (bot) {\n        return bot.id !== action.payload;\n      });\n    },\n    setSelectedChatbot: function setSelectedChatbot(state, action) {\n      state.selectedChatbot = action.payload;\n    },\n    setLoading: function setLoading(state, action) {\n      state.loading = action.payload;\n    },\n    setError: function setError(state, action) {\n      state.error = action.payload;\n    },\n    setSearchQuery: function setSearchQuery(state, action) {\n      state.searchQuery = action.payload;\n    },\n    setStatusFilter: function setStatusFilter(state, action) {\n      state.statusFilter = action.payload;\n    }\n  }\n});\nvar _chatbotsSlice$action = chatbotsSlice.actions,\n  setChatbots = _chatbotsSlice$action.setChatbots,\n  addChatbot = _chatbotsSlice$action.addChatbot,\n  updateChatbot = _chatbotsSlice$action.updateChatbot,\n  deleteChatbot = _chatbotsSlice$action.deleteChatbot,\n  setSelectedChatbot = _chatbotsSlice$action.setSelectedChatbot,\n  setLoading = _chatbotsSlice$action.setLoading,\n  setError = _chatbotsSlice$action.setError,\n  setSearchQuery = _chatbotsSlice$action.setSearchQuery,\n  setStatusFilter = _chatbotsSlice$action.setStatusFilter;\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (chatbotsSlice.reducer);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/redux/chatbots/chatbotsSlice.ts\n");

/***/ })

}]);