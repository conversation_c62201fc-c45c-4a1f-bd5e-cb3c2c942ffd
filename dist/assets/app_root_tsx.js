/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_root_tsx"],{

/***/ "./app/app.css":
/*!*********************!*\
  !*** ./app/app.css ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/styleDomAPI.js */ \"./node_modules/style-loader/dist/runtime/styleDomAPI.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/insertBySelector.js */ \"./node_modules/style-loader/dist/runtime/insertBySelector.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ \"./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/insertStyleElement.js */ \"./node_modules/style-loader/dist/runtime/insertStyleElement.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../node_modules/style-loader/dist/runtime/styleTagTransform.js */ \"./node_modules/style-loader/dist/runtime/styleTagTransform.js\");\n/* harmony import */ var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../node_modules/css-loader/dist/cjs.js!../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./app.css */ \"./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./app/app.css\");\n/* harmony import */ var _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n      \n      \n      \n      \n      \n      \n      \n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());\noptions.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());\noptions.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, \"head\");\noptions.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());\noptions.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());\n\nvar update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()((_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6___default()), options);\n\n\n\n\n       /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6___default()) && (_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6___default().locals) ? (_node_modules_css_loader_dist_cjs_js_node_modules_postcss_loader_dist_cjs_js_ruleSet_1_rules_1_use_2_app_css__WEBPACK_IMPORTED_MODULE_6___default().locals) : undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/app.css\n");

/***/ }),

/***/ "./app/components/AuthHydrator.tsx":
/*!*****************************************!*\
  !*** ./app/components/AuthHydrator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthHydrator)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../redux/auth/authSlice */ \"./app/redux/auth/authSlice.ts\");\n\n\n\n\n/**\n * Component that hydrates the auth state from localStorage on the client side\n * This prevents SSR/client hydration mismatches\n */\nfunction AuthHydrator() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    // Only run on client side\n    if (typeof window !== 'undefined') {\n      dispatch((0,_redux_auth_authSlice__WEBPACK_IMPORTED_MODULE_2__.hydrateAuth)());\n    }\n  }, [dispatch]);\n\n  // This component doesn't render anything\n  return null;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvY29tcG9uZW50cy9BdXRoSHlkcmF0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWtDO0FBQ29CO0FBQ0E7O0FBRXREO0FBQ0E7QUFDQTtBQUNBO0FBQ2UsU0FBU0csWUFBWUEsQ0FBQSxFQUFHO0VBQ3JDLElBQU1DLFFBQVEsR0FBR0gsa0VBQWMsQ0FBQyxDQUFDO0VBRWpDRCxnREFBUyxDQUFDLFlBQU07SUFDZDtJQUNBLElBQUksT0FBT0ssTUFBTSxLQUFLLFdBQVcsRUFBRTtNQUNqQ0QsUUFBUSxDQUFDRixrRUFBVyxDQUFDLENBQUMsQ0FBQztJQUN6QjtFQUNGLENBQUMsRUFBRSxDQUFDRSxRQUFRLENBQUMsQ0FBQzs7RUFFZDtFQUNBLE9BQU8sSUFBSTtBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL2NvbXBvbmVudHMvQXV0aEh5ZHJhdG9yLnRzeD83YTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUFwcERpc3BhdGNoIH0gZnJvbSAnLi4vaG9va3MvcmVkdXgtaG9va3MnO1xuaW1wb3J0IHsgaHlkcmF0ZUF1dGggfSBmcm9tICcuLi9yZWR1eC9hdXRoL2F1dGhTbGljZSc7XG5cbi8qKlxuICogQ29tcG9uZW50IHRoYXQgaHlkcmF0ZXMgdGhlIGF1dGggc3RhdGUgZnJvbSBsb2NhbFN0b3JhZ2Ugb24gdGhlIGNsaWVudCBzaWRlXG4gKiBUaGlzIHByZXZlbnRzIFNTUi9jbGllbnQgaHlkcmF0aW9uIG1pc21hdGNoZXNcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXV0aEh5ZHJhdG9yKCkge1xuICBjb25zdCBkaXNwYXRjaCA9IHVzZUFwcERpc3BhdGNoKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBPbmx5IHJ1biBvbiBjbGllbnQgc2lkZVxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgZGlzcGF0Y2goaHlkcmF0ZUF1dGgoKSk7XG4gICAgfVxuICB9LCBbZGlzcGF0Y2hdKTtcblxuICAvLyBUaGlzIGNvbXBvbmVudCBkb2Vzbid0IHJlbmRlciBhbnl0aGluZ1xuICByZXR1cm4gbnVsbDtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VBcHBEaXNwYXRjaCIsImh5ZHJhdGVBdXRoIiwiQXV0aEh5ZHJhdG9yIiwiZGlzcGF0Y2giLCJ3aW5kb3ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./app/components/AuthHydrator.tsx\n");

/***/ }),

/***/ "./app/components/builder/Builder.tsx":
/*!********************************************!*\
  !*** ./app/components/builder/Builder.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _BuilderTabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BuilderTabs */ \"./app/components/builder/BuilderTabs.tsx\");\n/* harmony import */ var _BuilderHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BuilderHeader */ \"./app/components/builder/BuilderHeader.tsx\");\n/* harmony import */ var _LeftNavigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LeftNavigation */ \"./app/components/builder/LeftNavigation.tsx\");\n/* harmony import */ var _FlowSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlowSidebar */ \"./app/components/builder/FlowSidebar.tsx\");\n/* harmony import */ var _FlowCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlowCanvas */ \"./app/components/builder/FlowCanvas.tsx\");\n/* harmony import */ var _NodesSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NodesSidebar */ \"./app/components/builder/NodesSidebar.tsx\");\n\n\n\n\n\n\n\n\nvar Builder = function Builder() {\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.builder;\n    }),\n    mode = _useAppSelector.mode;\n  var renderContent = function renderContent() {\n    switch (mode) {\n      case \"design\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex flex-1 overflow-hidden\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_FlowSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_FlowCanvas__WEBPACK_IMPORTED_MODULE_6__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_NodesSidebar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], null));\n      case \"train\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex-1 p-6\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"text-center py-12\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n          className: \"text-2xl\"\n        }, \"\\uD83E\\uDDE0\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\"\n        }, \"Training Mode\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n          className: \"text-gray-500\"\n        }, \"Train your chatbot with conversation data and improve its responses.\")));\n      case \"channels\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex-1 p-6\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"text-center py-12\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n          className: \"text-2xl\"\n        }, \"\\uD83D\\uDCF1\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\"\n        }, \"Channels\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n          className: \"text-gray-500\"\n        }, \"Configure deployment channels for your chatbot.\")));\n      case \"agent-transfer\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex-1 p-6\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"text-center py-12\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n          className: \"text-2xl\"\n        }, \"\\uD83D\\uDC65\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\"\n        }, \"Agent Transfer\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n          className: \"text-gray-500\"\n        }, \"Set up seamless handoff to human agents when needed.\")));\n      case \"integrations\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex-1 p-6\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"text-center py-12\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n          className: \"text-2xl\"\n        }, \"\\uD83D\\uDD17\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\"\n        }, \"Integrations\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n          className: \"text-gray-500\"\n        }, \"Connect your chatbot with external services and APIs.\")));\n      case \"settings\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex-1 p-6\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"text-center py-12\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n          className: \"text-2xl\"\n        }, \"\\u2699\\uFE0F\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\"\n        }, \"Settings\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n          className: \"text-gray-500\"\n        }, \"Configure your chatbot settings and preferences.\")));\n      default:\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n          className: \"flex flex-1 overflow-hidden\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_FlowSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_FlowCanvas__WEBPACK_IMPORTED_MODULE_6__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_NodesSidebar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], null));\n    }\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"h-screen flex bg-[#fafbfc]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_LeftNavigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 flex flex-col\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_BuilderHeader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_BuilderTabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], null), renderContent()));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Builder);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/Builder.tsx\n");

/***/ }),

/***/ "./app/components/builder/BuilderHeader.tsx":
/*!**************************************************!*\
  !*** ./app/components/builder/BuilderHeader.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar BuilderHeader = function BuilderHeader() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white border-b border-[#e2e8f0] px-6 py-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2 text-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-[#64748b] font-medium\"\n  }, \"NeuraTalk\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-3 h-3 text-[#cbd5e1]\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M9 5l7 7-7 7\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-[#3b82f6] font-medium\"\n  }, \"Create\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-[#f1f5f9] rounded-lg flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4 text-[#64748b]\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h1\", {\n    className: \"text-base font-semibold text-[#1e293b]\"\n  }, \"My Chatbot\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-3.5 h-3.5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-xs text-[#64748b] mt-0.5\"\n  }, \"Help customers navigate the digital purchasing process.\")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-[#64748b] bg-white border border-[#d1d5db] rounded-lg hover:bg-[#f9fafb] transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, \"PREVIEW\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-[#3b82f6] rounded-lg hover:bg-[#2563eb] transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, \"PUBLISH\")))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BuilderHeader);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/BuilderHeader.tsx\n");

/***/ }),

/***/ "./app/components/builder/BuilderTabs.tsx":
/*!************************************************!*\
  !*** ./app/components/builder/BuilderTabs.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/builder/builderSlice */ \"./app/redux/builder/builderSlice.ts\");\n\n\n\nvar BuilderTabs = function BuilderTabs() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.builder;\n    }),\n    mode = _useAppSelector.mode;\n  var tabs = [{\n    id: \"design\",\n    label: \"Design\"\n  }, {\n    id: \"train\",\n    label: \"Train\"\n  }, {\n    id: \"channels\",\n    label: \"Channels\"\n  }, {\n    id: \"agent-transfer\",\n    label: \"Agent Transfer\"\n  }, {\n    id: \"integrations\",\n    label: \"Integrations\"\n  }, {\n    id: \"settings\",\n    label: \"Settings\"\n  }];\n  var handleTabClick = function handleTabClick(tabId) {\n    dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.setMode)(tabId));\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-[#f8fafc] border-b border-[#e2e8f0]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex px-6\"\n  }, tabs.map(function (tab) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n      key: tab.id,\n      onClick: function onClick() {\n        return handleTabClick(tab.id);\n      },\n      className: \"\\n              px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 relative\\n              \".concat(mode === tab.id ? \"border-[#3b82f6] text-[#3b82f6] font-semibold bg-white\" : \"border-transparent text-[#64748b] hover:text-[#1e293b] hover:bg-white/50\", \"\\n            \"),\n      style: mode === tab.id ? {\n        borderRadius: \"6px 6px 0 0\",\n        marginBottom: \"-1px\"\n      } : {}\n    }, tab.label);\n  })));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BuilderTabs);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/BuilderTabs.tsx\n");

/***/ }),

/***/ "./app/components/builder/FlowCanvas.tsx":
/*!***********************************************!*\
  !*** ./app/components/builder/FlowCanvas.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/builder/builderSlice */ \"./app/redux/builder/builderSlice.ts\");\n/* harmony import */ var _NodeComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NodeComponent */ \"./app/components/builder/NodeComponent.tsx\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\n\nvar FlowCanvas = function FlowCanvas() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.builder;\n    }),\n    currentFlow = _useAppSelector.currentFlow,\n    selectedNode = _useAppSelector.selectedNode;\n  var canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isDragging = _useState2[0],\n    setIsDragging = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n      x: 0,\n      y: 0\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    dragOffset = _useState4[0],\n    setDragOffset = _useState4[1];\n  var handleCanvasClick = function handleCanvasClick(e) {\n    if (e.target === canvasRef.current) {\n      dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.setSelectedNode)(null));\n    }\n  };\n  var handleNodeClick = function handleNodeClick(node) {\n    dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.setSelectedNode)(node));\n  };\n  var handleNodeDragStart = function handleNodeDragStart(e, node) {\n    var _canvasRef$current;\n    setIsDragging(true);\n    var rect = (_canvasRef$current = canvasRef.current) === null || _canvasRef$current === void 0 ? void 0 : _canvasRef$current.getBoundingClientRect();\n    if (rect) {\n      setDragOffset({\n        x: e.clientX - rect.left - node.position.x,\n        y: e.clientY - rect.top - node.position.y\n      });\n    }\n  };\n  var handleNodeDrag = function handleNodeDrag(e, node) {\n    var _canvasRef$current2;\n    if (!isDragging) return;\n    var rect = (_canvasRef$current2 = canvasRef.current) === null || _canvasRef$current2 === void 0 ? void 0 : _canvasRef$current2.getBoundingClientRect();\n    if (rect) {\n      var newPosition = {\n        x: e.clientX - rect.left - dragOffset.x,\n        y: e.clientY - rect.top - dragOffset.y\n      };\n      dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.updateNode)(_objectSpread(_objectSpread({}, node), {}, {\n        position: newPosition\n      })));\n    }\n  };\n  var handleNodeDragEnd = function handleNodeDragEnd() {\n    setIsDragging(false);\n  };\n  var addNewNode = function addNewNode(type, position) {\n    var newNode = {\n      id: \"\".concat(type, \"-\").concat(Date.now()),\n      type: type,\n      position: position,\n      data: {\n        label: type.charAt(0).toUpperCase() + type.slice(1)\n      }\n    };\n    dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.addNode)(newNode));\n  };\n\n  // Create a default flow if none exists\n  var displayFlow = currentFlow || {\n    id: \"default\",\n    name: \"Default Flow\",\n    nodes: [{\n      id: \"start\",\n      type: \"message\",\n      position: {\n        x: 200,\n        y: 150\n      },\n      data: {\n        label: \"Start\"\n      }\n    }, {\n      id: \"message1\",\n      type: \"message\",\n      position: {\n        x: 450,\n        y: 250\n      },\n      data: {\n        label: \"Message\"\n      }\n    }],\n    connections: [{\n      id: \"start-message1\",\n      source: \"start\",\n      target: \"message1\"\n    }]\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 relative bg-[#fafbfc] overflow-hidden p-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-full h-full bg-white rounded-xl shadow-sm border border-[#e2e8f0] relative overflow-hidden\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    ref: canvasRef,\n    className: \"w-full h-full relative cursor-default\",\n    onClick: handleCanvasClick,\n    onMouseMove: function onMouseMove(e) {\n      if (selectedNode && isDragging) {\n        handleNodeDrag(e, selectedNode);\n      }\n    },\n    onMouseUp: handleNodeDragEnd\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute inset-0 opacity-15\",\n    style: {\n      backgroundImage: \"\\n                linear-gradient(rgba(148,163,184,0.2) 1px, transparent 1px),\\n                linear-gradient(90deg, rgba(148,163,184,0.2) 1px, transparent 1px)\\n              \",\n      backgroundSize: \"24px 24px\"\n    }\n  }), displayFlow.nodes.map(function (node) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_NodeComponent__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n      key: node.id,\n      node: node,\n      isSelected: (selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === node.id,\n      onClick: function onClick() {\n        return handleNodeClick(node);\n      },\n      onDragStart: function onDragStart(e) {\n        return handleNodeDragStart(e, node);\n      }\n    });\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"absolute inset-0 pointer-events-none\"\n  }, displayFlow.connections.map(function (connection) {\n    var sourceNode = displayFlow.nodes.find(function (n) {\n      return n.id === connection.source;\n    });\n    var targetNode = displayFlow.nodes.find(function (n) {\n      return n.id === connection.target;\n    });\n    if (!sourceNode || !targetNode) return null;\n    var startX = sourceNode.position.x + 80; // Node width / 2\n    var startY = sourceNode.position.y + 25; // Node height / 2\n    var endX = targetNode.position.x + 80;\n    var endY = targetNode.position.y + 25;\n\n    // Create curved path\n    var midX = (startX + endX) / 2;\n    var midY = (startY + endY) / 2;\n    var controlX = midX;\n    var controlY = startY;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      key: connection.id,\n      d: \"M \".concat(startX, \" \").concat(startY, \" Q \").concat(controlX, \" \").concat(controlY, \" \").concat(endX, \" \").concat(endY),\n      stroke: \"#94a3b8\",\n      strokeWidth: \"2\",\n      fill: \"none\",\n      markerEnd: \"url(#arrowhead)\"\n    });\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"defs\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"marker\", {\n    id: \"arrowhead\",\n    markerWidth: \"8\",\n    markerHeight: \"6\",\n    refX: \"7\",\n    refY: \"3\",\n    orient: \"auto\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"polygon\", {\n    points: \"0 0, 8 3, 0 6\",\n    fill: \"#94a3b8\"\n  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute bottom-6 right-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: function onClick() {\n      return addNewNode(\"message\", {\n        x: 300,\n        y: 200\n      });\n    },\n    className: \"w-12 h-12 bg-[#3b82f6] text-white rounded-full shadow-lg hover:bg-[#2563eb] transition-colors flex items-center justify-center\",\n    title: \"Add Node\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-6 h-6\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n  }))))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlowCanvas);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/FlowCanvas.tsx\n");

/***/ }),

/***/ "./app/components/builder/FlowSidebar.tsx":
/*!************************************************!*\
  !*** ./app/components/builder/FlowSidebar.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/builder/builderSlice */ \"./app/redux/builder/builderSlice.ts\");\n\n\n\nvar FlowSidebar = function FlowSidebar() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.builder;\n    }),\n    flows = _useAppSelector.flows,\n    currentFlow = _useAppSelector.currentFlow;\n  var handleFlowSelect = function handleFlowSelect(flowId) {\n    var flow = flows.find(function (f) {\n      return f.id === flowId;\n    });\n    if (flow) {\n      dispatch((0,_redux_builder_builderSlice__WEBPACK_IMPORTED_MODULE_2__.setCurrentFlow)(flow));\n    }\n  };\n  var addNewFlow = function addNewFlow() {\n    // This would typically open a modal or form to create a new flow\n    console.log(\"Add new flow\");\n  };\n\n  // Default flows for display\n  var defaultFlows = [{\n    id: \"welcome\",\n    name: \"Welcome\",\n    status: \"Default\"\n  }, {\n    id: \"fallback\",\n    name: \"Fallback\",\n    status: \"Default\"\n  }];\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-[240px] p-4 flex flex-col\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white rounded-xl shadow-lg border border-[#e2e8f0] flex flex-col h-full\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"px-4 py-3 border-b border-[#f1f5f9]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-sm font-semibold text-[#1e293b]\"\n  }, \"Flows\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: addNewFlow,\n    className: \"w-6 h-6 bg-[#3b82f6] text-white rounded-lg flex items-center justify-center hover:bg-[#2563eb] transition-colors shadow-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-3 h-3\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 4v16m8-8H4\"\n  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 overflow-y-auto p-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"space-y-2\"\n  }, defaultFlows.map(function (flow) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      key: flow.id,\n      className: \"flex items-center justify-between px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 \".concat((currentFlow === null || currentFlow === void 0 ? void 0 : currentFlow.id) === flow.id ? \"bg-[#eff6ff] border border-[#dbeafe] shadow-sm\" : \"hover:bg-[#f8fafc] hover:shadow-sm\"),\n      onClick: function onClick() {\n        return handleFlowSelect(flow.id);\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-center space-x-3\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"w-8 h-8 bg-[#f1f5f9] rounded-lg flex items-center justify-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-4 h-4 text-[#64748b]\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"text-sm font-medium text-[#1e293b]\"\n    }, flow.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"text-xs text-[#64748b]\"\n    }, flow.status))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n      className: \"p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-4 h-4\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n    }))));\n  })))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlowSidebar);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/FlowSidebar.tsx\n");

/***/ }),

/***/ "./app/components/builder/LeftNavigation.tsx":
/*!***************************************************!*\
  !*** ./app/components/builder/LeftNavigation.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar LeftNavigation = function LeftNavigation() {\n  var navigationItems = [{\n    id: \"home\",\n    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      d: \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"\n    })),\n    active: false\n  }, {\n    id: \"chat\",\n    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      d: \"M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"\n    })),\n    active: true\n  }, {\n    id: \"flows\",\n    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n    })),\n    active: false\n  }, {\n    id: \"analytics\",\n    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"\n    })),\n    active: false\n  }, {\n    id: \"settings\",\n    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-5 h-5\",\n      fill: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      d: \"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z\"\n    })),\n    active: false\n  }];\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-[52px] bg-[#334155] flex flex-col items-center py-4 border-r border-[#475569]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-[#3b82f6] rounded-lg flex items-center justify-center mb-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5 text-white\",\n    fill: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex flex-col space-y-2\"\n  }, navigationItems.map(function (item) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n      key: item.id,\n      className: \"\\n              w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200\\n              \".concat(item.active ? \"bg-[#3b82f6] text-white shadow-lg\" : \"text-[#94a3b8] hover:text-white hover:bg-[#475569]\", \"\\n            \")\n    }, item.icon);\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-auto\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"w-10 h-10 rounded-lg flex items-center justify-center text-[#94a3b8] hover:text-white hover:bg-[#475569] transition-all duration-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n  })))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LeftNavigation);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/LeftNavigation.tsx\n");

/***/ }),

/***/ "./app/components/builder/NodeComponent.tsx":
/*!**************************************************!*\
  !*** ./app/components/builder/NodeComponent.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NodeComponent = function NodeComponent(_ref) {\n  var node = _ref.node,\n    isSelected = _ref.isSelected,\n    _onClick = _ref.onClick,\n    onDragStart = _ref.onDragStart;\n  var getNodeIcon = function getNodeIcon(type) {\n    switch (type) {\n      case \"start\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M8 5v14l11-7z\"\n        }));\n      case \"message\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"\n        }));\n      case \"interactive\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n        }));\n      case \"feedback\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\"\n        }));\n      case \"notification\":\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"\n        }));\n      default:\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n          className: \"w-3.5 h-3.5 text-white\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 24 24\"\n        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n          d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z\"\n        }));\n    }\n  };\n  var getNodeColor = function getNodeColor(type) {\n    switch (type) {\n      case \"start\":\n        return \"bg-white border-[#10b981] shadow-sm\";\n      case \"message\":\n        return \"bg-white border-[#3b82f6] shadow-sm\";\n      case \"interactive\":\n        return \"bg-white border-[#10b981] shadow-sm\";\n      case \"feedback\":\n        return \"bg-white border-[#f59e0b] shadow-sm\";\n      case \"notification\":\n        return \"bg-white border-[#8b5cf6] shadow-sm\";\n      default:\n        return \"bg-white border-[#6b7280] shadow-sm\";\n    }\n  };\n  var getIconBgColor = function getIconBgColor(type) {\n    switch (type) {\n      case \"start\":\n        return \"bg-[#10b981]\";\n      case \"message\":\n        return \"bg-[#3b82f6]\";\n      case \"interactive\":\n        return \"bg-[#10b981]\";\n      case \"feedback\":\n        return \"bg-[#f59e0b]\";\n      case \"notification\":\n        return \"bg-[#8b5cf6]\";\n      default:\n        return \"bg-[#6b7280]\";\n    }\n  };\n  var getNodeLabel = function getNodeLabel(node) {\n    return node.data.label || node.type.charAt(0).toUpperCase() + node.type.slice(1);\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute cursor-move select-none transition-all duration-200 \".concat(isSelected ? \"z-10\" : \"z-0\"),\n    style: {\n      left: node.position.x,\n      top: node.position.y,\n      transform: isSelected ? \"scale(1.05)\" : \"scale(1)\"\n    },\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      _onClick();\n    },\n    onMouseDown: onDragStart\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"\\n          w-[160px] min-h-[50px] rounded-lg border p-3 shadow-sm hover:shadow-md transition-all duration-200\\n          \".concat(getNodeColor(node.type), \"\\n          \").concat(isSelected ? \"ring-2 ring-[#3b82f6] ring-offset-2\" : \"\", \"\\n        \")\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2 mb-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-6 h-6 rounded flex items-center justify-center \".concat(getIconBgColor(node.type))\n  }, getNodeIcon(node.type)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"font-medium text-sm text-[#1e293b]\"\n  }, getNodeLabel(node))), node.data.message && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-gray-600 mb-2 line-clamp-2\"\n  }, node.data.message), node.data.options && node.data.options.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"space-y-1\"\n  }, node.data.options.slice(0, 2).map(function (option, index) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      key: index,\n      className: \"text-xs bg-white bg-opacity-50 rounded px-2 py-1\"\n    }, option);\n  }), node.data.options.length > 2 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-gray-500\"\n  }, \"+\", node.data.options.length - 2, \" more\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute -right-1.5 top-1/2 transform -translate-y-1/2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute -left-1.5 top-1/2 transform -translate-y-1/2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer\"\n  }))), isSelected && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute -top-8 right-0 flex space-x-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-gray-50 transition-colors\",\n    title: \"Edit\"\n  }, \"\\u270F\\uFE0F\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-red-50 hover:border-red-300 transition-colors\",\n    title: \"Delete\"\n  }, \"\\uD83D\\uDDD1\\uFE0F\")));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NodeComponent);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/NodeComponent.tsx\n");

/***/ }),

/***/ "./app/components/builder/NodesSidebar.tsx":
/*!*************************************************!*\
  !*** ./app/components/builder/NodesSidebar.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NodesSidebar = function NodesSidebar() {\n  var nodeCategories = [{\n    title: \"Usage\",\n    color: \"bg-[#3b82f6]\",\n    nodes: [{\n      id: \"message\",\n      label: \"Message\",\n      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n        className: \"w-4 h-4 text-[#3b82f6]\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        d: \"M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"\n      }))\n    }, {\n      id: \"interactive\",\n      label: \"Interactive Message\",\n      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n        className: \"w-4 h-4 text-[#10b981]\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n      }))\n    }, {\n      id: \"feedback\",\n      label: \"Feedback\",\n      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n        className: \"w-4 h-4 text-[#f59e0b]\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        d: \"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\"\n      }))\n    }]\n  }, {\n    title: \"Utilities\",\n    color: \"bg-[#6366f1]\",\n    nodes: [{\n      id: \"utilities\",\n      label: \"Utilities\",\n      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n        className: \"w-4 h-4 text-[#6366f1]\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"\n      }))\n    }]\n  }, {\n    title: \"Marketplace\",\n    color: \"bg-[#8b5cf6]\",\n    nodes: [{\n      id: \"notification\",\n      label: \"Notification\",\n      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n        className: \"w-4 h-4 text-[#8b5cf6]\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n        d: \"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"\n      }))\n    }]\n  }];\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-[280px] p-4 flex flex-col\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white rounded-xl shadow-lg border border-[#e2e8f0] flex flex-col h-full\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"px-4 py-3 border-b border-[#f1f5f9]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-sm font-semibold text-[#1e293b]\"\n  }, \"Nodes\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 overflow-y-auto p-3 space-y-3\"\n  }, nodeCategories.map(function (category) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      key: category.title,\n      className: \"bg-[#f8fafc] rounded-lg border border-[#e2e8f0] p-3\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-center space-x-2 mb-3\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"w-3 h-3 \".concat(category.color, \" rounded-full\")\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h4\", {\n      className: \"text-xs font-semibold text-[#1e293b] uppercase tracking-wide\"\n    }, category.title)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"grid grid-cols-2 gap-2\"\n    }, category.nodes.map(function (node) {\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n        key: node.id,\n        className: \"bg-white border border-[#e2e8f0] rounded-lg p-3 cursor-pointer hover:shadow-md hover:border-[#3b82f6] transition-all duration-200 group\",\n        draggable: true,\n        onDragStart: function onDragStart(e) {\n          e.dataTransfer.setData(\"application/reactflow\", node.id);\n          e.dataTransfer.effectAllowed = \"move\";\n        }\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n        className: \"flex flex-col items-center space-y-2\"\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n        className: \"w-8 h-8 bg-[#f8fafc] rounded-lg flex items-center justify-center group-hover:bg-[#eff6ff] transition-colors\"\n      }, node.icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n        className: \"text-xs font-medium text-[#1e293b] text-center leading-tight\"\n      }, node.label)));\n    })));\n  }))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NodesSidebar);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/builder/NodesSidebar.tsx\n");

/***/ }),

/***/ "./app/components/chat/ChatList.tsx":
/*!******************************************!*\
  !*** ./app/components/chat/ChatList.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/chat/chatSlice */ \"./app/redux/chat/chatSlice.ts\");\n\n\n\nvar ChatList = function ChatList() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.chat;\n    }),\n    sessions = _useAppSelector.sessions,\n    activeSession = _useAppSelector.activeSession,\n    statusFilter = _useAppSelector.statusFilter,\n    searchQuery = _useAppSelector.searchQuery;\n  var statusCounts = {\n    active: sessions.filter(function (s) {\n      return s.status === 'active';\n    }).length,\n    queued: sessions.filter(function (s) {\n      return s.status === 'queued';\n    }).length,\n    archived: sessions.filter(function (s) {\n      return s.status === 'archived';\n    }).length,\n    missed: sessions.filter(function (s) {\n      return s.status === 'missed';\n    }).length\n  };\n  var filteredSessions = sessions.filter(function (session) {\n    var matchesSearch = session.userName.toLowerCase().includes(searchQuery.toLowerCase()) || session.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());\n    var matchesStatus = statusFilter === 'all' || session.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n  var getStatusColor = function getStatusColor(status) {\n    switch (status) {\n      case 'active':\n        return 'bg-green-500';\n      case 'queued':\n        return 'bg-blue-500';\n      case 'archived':\n        return 'bg-gray-400';\n      case 'missed':\n        return 'bg-red-500';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n  var getStatusLabel = function getStatusLabel(status) {\n    switch (status) {\n      case 'active':\n        return 'Active';\n      case 'queued':\n        return 'Queued';\n      case 'archived':\n        return 'Archived';\n      case 'missed':\n        return 'Missed';\n      default:\n        return status;\n    }\n  };\n  var handleSessionClick = function handleSessionClick(session) {\n    dispatch((0,_redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__.setActiveSession)(session));\n  };\n  var handleSearchChange = function handleSearchChange(e) {\n    dispatch((0,_redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__.setSearchQuery)(e.target.value));\n  };\n  var handleStatusFilterChange = function handleStatusFilterChange(status) {\n    dispatch((0,_redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__.setStatusFilter)(status));\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-80 bg-white border-r border-gray-200 flex flex-col\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-b border-gray-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h2\", {\n    className: \"text-xl font-semibold text-gray-900 mb-4\"\n  }, \"Chats\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex space-x-1 mb-4\"\n  }, [{\n    key: 'active',\n    label: 'Active',\n    count: statusCounts.active\n  }, {\n    key: 'queued',\n    label: 'Queued',\n    count: statusCounts.queued\n  }, {\n    key: 'archived',\n    label: 'Archived',\n    count: statusCounts.archived\n  }, {\n    key: 'missed',\n    label: 'Missed',\n    count: statusCounts.missed\n  }].map(function (filter) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n      key: filter.key,\n      onClick: function onClick() {\n        return handleStatusFilterChange(filter.key);\n      },\n      className: \"\\n                flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors\\n                \".concat(statusFilter === filter.key ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200', \"\\n              \")\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, filter.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n      className: \"bg-white bg-opacity-70 px-1 rounded-full\"\n    }, filter.count));\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"h-4 w-4 text-gray-400\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"Search conversations...\",\n    value: searchQuery,\n    onChange: handleSearchChange,\n    className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 overflow-y-auto\"\n  }, filteredSessions.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-center py-8\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-6 h-6 text-gray-400\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-sm text-gray-500\"\n  }, \"No conversations found\")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"divide-y divide-gray-100\"\n  }, filteredSessions.map(function (session) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      key: session.id,\n      onClick: function onClick() {\n        return handleSessionClick(session);\n      },\n      className: \"\\n                  p-4 cursor-pointer hover:bg-gray-50 transition-colors\\n                  \".concat((activeSession === null || activeSession === void 0 ? void 0 : activeSession.id) === session.id ? 'bg-blue-50 border-r-2 border-blue-500' : '', \"\\n                \")\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-start space-x-3\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"relative\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n      className: \"text-sm font-medium text-gray-600\"\n    }, session.userName.split(' ').map(function (n) {\n      return n[0];\n    }).join(''))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white \".concat(getStatusColor(session.status))\n    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex-1 min-w-0\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-center justify-between\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-sm font-medium text-gray-900 truncate\"\n    }, session.userName), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-xs text-gray-500\"\n    }, session.timestamp)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-sm text-gray-600 truncate mt-1\"\n    }, session.lastMessage), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-center justify-between mt-2\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n      className: \"\\n                        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\\n                        \".concat(session.status === 'active' ? 'bg-green-100 text-green-800' : session.status === 'queued' ? 'bg-blue-100 text-blue-800' : session.status === 'archived' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800', \"\\n                      \")\n    }, getStatusLabel(session.status))))));\n  }))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatList);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/chat/ChatList.tsx\n");

/***/ }),

/***/ "./app/components/chat/ChatWindow.tsx":
/*!********************************************!*\
  !*** ./app/components/chat/ChatWindow.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/chat/chatSlice */ \"./app/redux/chat/chatSlice.ts\");\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n\nvar ChatWindow = function ChatWindow() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.chat;\n    }),\n    activeSession = _useAppSelector.activeSession;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    inputMessage = _useState2[0],\n    setInputMessage = _useState2[1];\n  var handleSendMessage = function handleSendMessage() {\n    if (inputMessage.trim() && activeSession) {\n      var newMessage = {\n        id: Date.now().toString(),\n        content: inputMessage,\n        sender: 'bot',\n        timestamp: new Date().toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      };\n      dispatch((0,_redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__.addMessage)({\n        sessionId: activeSession.id,\n        message: newMessage\n      }));\n      setInputMessage('');\n    }\n  };\n  var handleKeyPress = function handleKeyPress(e) {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  var handleEndChat = function handleEndChat() {\n    if (activeSession) {\n      dispatch((0,_redux_chat_chatSlice__WEBPACK_IMPORTED_MODULE_2__.updateSessionStatus)({\n        sessionId: activeSession.id,\n        status: 'archived'\n      }));\n    }\n  };\n  if (!activeSession) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex-1 flex items-center justify-center bg-gray-50\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"text-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-8 h-8 text-gray-400\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n      className: \"text-lg font-medium text-gray-900 mb-2\"\n    }, \"No conversation selected\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-gray-500\"\n    }, \"Select a conversation from the sidebar to start chatting.\")));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 flex flex-col bg-white\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-b border-gray-200 bg-white\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-sm font-medium text-gray-600\"\n  }, activeSession.userName.split(' ').map(function (n) {\n    return n[0];\n  }).join(''))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-lg font-semibold text-gray-900\"\n  }, activeSession.userName), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-sm text-gray-500\"\n  }, \"A logged user\"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n    title: \"Call\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n    title: \"Video call\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: handleEndChat,\n    className: \"px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors\"\n  }, \"END CHAT\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n  })))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 overflow-y-auto p-4 space-y-4\"\n  }, activeSession.messages.map(function (message) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      key: message.id,\n      className: \"flex \".concat(message.sender === 'user' ? 'justify-end' : 'justify-start')\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"\\n                max-w-xs lg:max-w-md px-4 py-2 rounded-lg text-sm\\n                \".concat(message.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900', \"\\n              \")\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", null, message.content), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-xs mt-1 \".concat(message.sender === 'user' ? 'text-blue-100' : 'text-gray-500')\n    }, message.timestamp)));\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-t border-gray-200 bg-white\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n    title: \"Magic Write\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-sm\"\n  }, \"\\u2728\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n    title: \"Translation\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-sm\"\n  }, \"\\uD83C\\uDF10\"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"input\", {\n    type: \"text\",\n    value: inputMessage,\n    onChange: function onChange(e) {\n      return setInputMessage(e.target.value);\n    },\n    onKeyPress: handleKeyPress,\n    placeholder: \"Hello! How can I assist you today?\",\n    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: handleSendMessage,\n    disabled: !inputMessage.trim(),\n    className: \"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n  })))))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatWindow);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/chat/ChatWindow.tsx\n");

/***/ }),

/***/ "./app/components/settings/SettingsCard.tsx":
/*!**************************************************!*\
  !*** ./app/components/settings/SettingsCard.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar SettingsCard = function SettingsCard(_ref) {\n  var title = _ref.title,\n    description = _ref.description,\n    icon = _ref.icon,\n    children = _ref.children,\n    onClick = _ref.onClick;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 \".concat(onClick ? 'cursor-pointer' : ''),\n    onClick: onClick\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-start space-x-4 mb-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-2xl\"\n  }, icon)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-lg font-semibold text-gray-900 mb-2\"\n  }, title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-sm text-gray-600\"\n  }, description))), children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-4\"\n  }, children));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsCard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/settings/SettingsCard.tsx\n");

/***/ }),

/***/ "./app/root.tsx":
/*!**********************!*\
  !*** ./app/root.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   Layout: () => (/* binding */ Layout),\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./redux/store */ \"./app/redux/store.ts\");\n/* harmony import */ var _components_AuthHydrator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/AuthHydrator */ \"./app/components/AuthHydrator.tsx\");\n/* harmony import */ var _app_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./app.css */ \"./app/app.css\");\n/* harmony import */ var _routes_Home__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./routes/Home */ \"./app/routes/Home/index.tsx\");\n/* harmony import */ var _routes_Agents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./routes/Agents */ \"./app/routes/Agents/index.tsx\");\n/* harmony import */ var _routes_Agents_new__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./routes/Agents/new */ \"./app/routes/Agents/new.tsx\");\n/* harmony import */ var _routes_Builder__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./routes/Builder */ \"./app/routes/Builder/index.tsx\");\n/* harmony import */ var _routes_Chat__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./routes/Chat */ \"./app/routes/Chat/index.tsx\");\n/* harmony import */ var _routes_Settings__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./routes/Settings */ \"./app/routes/Settings/index.tsx\");\n\n\n\n\n\n\n\n// Import route components\n\n\n\n\n\n\n\n// Layout component for the HTML structure\nfunction Layout(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"html\", {\n    lang: \"en\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"head\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width, initial-scale=1\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"title\", null, \"NeuraTalk AI - Dashboard\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"link\", {\n    rel: \"preconnect\",\n    href: \"https://fonts.googleapis.com\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"link\", {\n    rel: \"preconnect\",\n    href: \"https://fonts.gstatic.com\",\n    crossOrigin: \"anonymous\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"link\", {\n    rel: \"stylesheet\",\n    href: \"https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"body\", null, children));\n}\n\n// Main App component with React Router DOM v6\nfunction App() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {\n    store: _redux_store__WEBPACK_IMPORTED_MODULE_3__.store\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_AuthHydrator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.BrowserRouter, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Routes, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Home__WEBPACK_IMPORTED_MODULE_6__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/agents\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Agents__WEBPACK_IMPORTED_MODULE_7__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/agents/new\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Agents_new__WEBPACK_IMPORTED_MODULE_8__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/builder/:id\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Builder__WEBPACK_IMPORTED_MODULE_9__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/chat\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Chat__WEBPACK_IMPORTED_MODULE_10__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"/settings/:id\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_routes_Settings__WEBPACK_IMPORTED_MODULE_11__[\"default\"], null)\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Route, {\n    path: \"*\",\n    element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ErrorBoundary, null)\n  }))));\n}\n\n// Error boundary component for 404 and other errors\nfunction ErrorBoundary() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"main\", {\n    className: \"pt-16 p-4 container mx-auto\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h1\", null, \"404\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", null, \"The requested page could not be found.\"));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm9vdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQzhDO0FBQ2pDO0FBQ0Q7QUFDZTtBQUNsQzs7QUFFbkI7QUFDaUM7QUFDSTtBQUNNO0FBQ0o7QUFDTjtBQUNROztBQUV6QztBQUNPLFNBQVNhLE1BQU1BLENBQUFDLElBQUEsRUFBOEM7RUFBQSxJQUEzQ0MsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7RUFDL0Isb0JBQ0VmLDBEQUFBO0lBQU1pQixJQUFJLEVBQUM7RUFBSSxnQkFDYmpCLDBEQUFBLDRCQUNFQSwwREFBQTtJQUFNa0IsT0FBTyxFQUFDO0VBQU8sQ0FBRSxDQUFDLGVBQ3hCbEIsMERBQUE7SUFBTW1CLElBQUksRUFBQyxVQUFVO0lBQUNDLE9BQU8sRUFBQztFQUFxQyxDQUFFLENBQUMsZUFDdEVwQiwwREFBQSxnQkFBTywwQkFBK0IsQ0FBQyxlQUN2Q0EsMERBQUE7SUFBTXFCLEdBQUcsRUFBQyxZQUFZO0lBQUNDLElBQUksRUFBQztFQUE4QixDQUFFLENBQUMsZUFDN0R0QiwwREFBQTtJQUNFcUIsR0FBRyxFQUFDLFlBQVk7SUFDaEJDLElBQUksRUFBQywyQkFBMkI7SUFDaENDLFdBQVcsRUFBQztFQUFXLENBQ3hCLENBQUMsZUFDRnZCLDBEQUFBO0lBQ0VxQixHQUFHLEVBQUMsWUFBWTtJQUNoQkMsSUFBSSxFQUFDO0VBQWdILENBQ3RILENBQ0csQ0FBQyxlQUNQdEIsMERBQUEsZUFBT2UsUUFBZSxDQUNsQixDQUFDO0FBRVg7O0FBRUE7QUFDZSxTQUFTUyxHQUFHQSxDQUFBLEVBQUc7RUFDNUIsb0JBQ0V4QiwwREFBQSxDQUFDSSxpREFBUTtJQUFDQyxLQUFLLEVBQUVBLCtDQUFLQTtFQUFDLGdCQUNyQkwsMERBQUEsQ0FBQ00sZ0VBQVksTUFBRSxDQUFDLGVBQ2hCTiwwREFBQSxDQUFDQywyREFBYSxxQkFDWkQsMERBQUEsQ0FBQ0Usb0RBQU0scUJBQ0xGLDBEQUFBLENBQUNHLG1EQUFLO0lBQUNzQixJQUFJLEVBQUMsR0FBRztJQUFDQyxPQUFPLGVBQUUxQiwwREFBQSxDQUFDTyxvREFBSSxNQUFFO0VBQUUsQ0FBRSxDQUFDLGVBQ3JDUCwwREFBQSxDQUFDRyxtREFBSztJQUFDc0IsSUFBSSxFQUFDLFNBQVM7SUFBQ0MsT0FBTyxlQUFFMUIsMERBQUEsQ0FBQ1Esc0RBQU0sTUFBRTtFQUFFLENBQUUsQ0FBQyxlQUM3Q1IsMERBQUEsQ0FBQ0csbURBQUs7SUFBQ3NCLElBQUksRUFBQyxhQUFhO0lBQUNDLE9BQU8sZUFBRTFCLDBEQUFBLENBQUNTLDBEQUFRLE1BQUU7RUFBRSxDQUFFLENBQUMsZUFDbkRULDBEQUFBLENBQUNHLG1EQUFLO0lBQUNzQixJQUFJLEVBQUMsY0FBYztJQUFDQyxPQUFPLGVBQUUxQiwwREFBQSxDQUFDVSx1REFBTyxNQUFFO0VBQUUsQ0FBRSxDQUFDLGVBQ25EViwwREFBQSxDQUFDRyxtREFBSztJQUFDc0IsSUFBSSxFQUFDLE9BQU87SUFBQ0MsT0FBTyxlQUFFMUIsMERBQUEsQ0FBQ1cscURBQUksTUFBRTtFQUFFLENBQUUsQ0FBQyxlQUN6Q1gsMERBQUEsQ0FBQ0csbURBQUs7SUFBQ3NCLElBQUksRUFBQyxlQUFlO0lBQUNDLE9BQU8sZUFBRTFCLDBEQUFBLENBQUNZLHlEQUFRLE1BQUU7RUFBRSxDQUFFLENBQUMsZUFDckRaLDBEQUFBLENBQUNHLG1EQUFLO0lBQUNzQixJQUFJLEVBQUMsR0FBRztJQUFDQyxPQUFPLGVBQUUxQiwwREFBQSxDQUFDMkIsYUFBYSxNQUFFO0VBQUUsQ0FBRSxDQUN2QyxDQUNLLENBQ1AsQ0FBQztBQUVmOztBQUVBO0FBQ08sU0FBU0EsYUFBYUEsQ0FBQSxFQUFHO0VBQzlCLG9CQUNFM0IsMERBQUE7SUFBTTRCLFNBQVMsRUFBQztFQUE2QixnQkFDM0M1QiwwREFBQSxhQUFJLEtBQU8sQ0FBQyxlQUNaQSwwREFBQSxZQUFHLHdDQUF5QyxDQUN4QyxDQUFDO0FBRVgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib3QtdWkvLi9hcHAvcm9vdC50c3g/YjNlMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBCcm93c2VyUm91dGVyLCBSb3V0ZXMsIFJvdXRlLCBPdXRsZXQgfSBmcm9tIFwicmVhY3Qtcm91dGVyLWRvbVwiO1xuaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcbmltcG9ydCB7IHN0b3JlIH0gZnJvbSBcIi4vcmVkdXgvc3RvcmVcIjtcbmltcG9ydCBBdXRoSHlkcmF0b3IgZnJvbSBcIi4vY29tcG9uZW50cy9BdXRoSHlkcmF0b3JcIjtcbmltcG9ydCBcIi4vYXBwLmNzc1wiO1xuXG4vLyBJbXBvcnQgcm91dGUgY29tcG9uZW50c1xuaW1wb3J0IEhvbWUgZnJvbSBcIi4vcm91dGVzL0hvbWVcIjtcbmltcG9ydCBBZ2VudHMgZnJvbSBcIi4vcm91dGVzL0FnZW50c1wiO1xuaW1wb3J0IE5ld0FnZW50IGZyb20gXCIuL3JvdXRlcy9BZ2VudHMvbmV3XCI7XG5pbXBvcnQgQnVpbGRlciBmcm9tIFwiLi9yb3V0ZXMvQnVpbGRlclwiO1xuaW1wb3J0IENoYXQgZnJvbSBcIi4vcm91dGVzL0NoYXRcIjtcbmltcG9ydCBTZXR0aW5ncyBmcm9tIFwiLi9yb3V0ZXMvU2V0dGluZ3NcIjtcblxuLy8gTGF5b3V0IGNvbXBvbmVudCBmb3IgdGhlIEhUTUwgc3RydWN0dXJlXG5leHBvcnQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8bWV0YSBjaGFyU2V0PVwidXRmLThcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICA8dGl0bGU+TmV1cmFUYWxrIEFJIC0gRGFzaGJvYXJkPC90aXRsZT5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmtcbiAgICAgICAgICByZWw9XCJwcmVjb25uZWN0XCJcbiAgICAgICAgICBocmVmPVwiaHR0cHM6Ly9mb250cy5nc3RhdGljLmNvbVwiXG4gICAgICAgICAgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIlxuICAgICAgICAvPlxuICAgICAgICA8bGlua1xuICAgICAgICAgIHJlbD1cInN0eWxlc2hlZXRcIlxuICAgICAgICAgIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOml0YWwsb3Bzeix3Z2h0QDAsMTQuLjMyLDEwMC4uOTAwOzEsMTQuLjMyLDEwMC4uOTAwJmRpc3BsYXk9c3dhcFwiXG4gICAgICAgIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cblxuLy8gTWFpbiBBcHAgY29tcG9uZW50IHdpdGggUmVhY3QgUm91dGVyIERPTSB2NlxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKCkge1xuICByZXR1cm4gKFxuICAgIDxQcm92aWRlciBzdG9yZT17c3RvcmV9PlxuICAgICAgPEF1dGhIeWRyYXRvciAvPlxuICAgICAgPEJyb3dzZXJSb3V0ZXI+XG4gICAgICAgIDxSb3V0ZXM+XG4gICAgICAgICAgPFJvdXRlIHBhdGg9XCIvXCIgZWxlbWVudD17PEhvbWUgLz59IC8+XG4gICAgICAgICAgPFJvdXRlIHBhdGg9XCIvYWdlbnRzXCIgZWxlbWVudD17PEFnZW50cyAvPn0gLz5cbiAgICAgICAgICA8Um91dGUgcGF0aD1cIi9hZ2VudHMvbmV3XCIgZWxlbWVudD17PE5ld0FnZW50IC8+fSAvPlxuICAgICAgICAgIDxSb3V0ZSBwYXRoPVwiL2J1aWxkZXIvOmlkXCIgZWxlbWVudD17PEJ1aWxkZXIgLz59IC8+XG4gICAgICAgICAgPFJvdXRlIHBhdGg9XCIvY2hhdFwiIGVsZW1lbnQ9ezxDaGF0IC8+fSAvPlxuICAgICAgICAgIDxSb3V0ZSBwYXRoPVwiL3NldHRpbmdzLzppZFwiIGVsZW1lbnQ9ezxTZXR0aW5ncyAvPn0gLz5cbiAgICAgICAgICA8Um91dGUgcGF0aD1cIipcIiBlbGVtZW50PXs8RXJyb3JCb3VuZGFyeSAvPn0gLz5cbiAgICAgICAgPC9Sb3V0ZXM+XG4gICAgICA8L0Jyb3dzZXJSb3V0ZXI+XG4gICAgPC9Qcm92aWRlcj5cbiAgKTtcbn1cblxuLy8gRXJyb3IgYm91bmRhcnkgY29tcG9uZW50IGZvciA0MDQgYW5kIG90aGVyIGVycm9yc1xuZXhwb3J0IGZ1bmN0aW9uIEVycm9yQm91bmRhcnkoKSB7XG4gIHJldHVybiAoXG4gICAgPG1haW4gY2xhc3NOYW1lPVwicHQtMTYgcC00IGNvbnRhaW5lciBteC1hdXRvXCI+XG4gICAgICA8aDE+NDA0PC9oMT5cbiAgICAgIDxwPlRoZSByZXF1ZXN0ZWQgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQuPC9wPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkJyb3dzZXJSb3V0ZXIiLCJSb3V0ZXMiLCJSb3V0ZSIsIlByb3ZpZGVyIiwic3RvcmUiLCJBdXRoSHlkcmF0b3IiLCJIb21lIiwiQWdlbnRzIiwiTmV3QWdlbnQiLCJCdWlsZGVyIiwiQ2hhdCIsIlNldHRpbmdzIiwiTGF5b3V0IiwiX3JlZiIsImNoaWxkcmVuIiwiY3JlYXRlRWxlbWVudCIsImxhbmciLCJjaGFyU2V0IiwibmFtZSIsImNvbnRlbnQiLCJyZWwiLCJocmVmIiwiY3Jvc3NPcmlnaW4iLCJBcHAiLCJwYXRoIiwiZWxlbWVudCIsIkVycm9yQm91bmRhcnkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./app/root.tsx\n");

/***/ }),

/***/ "./app/routes/Agents/index.tsx":
/*!*************************************!*\
  !*** ./app/routes/Agents/index.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Agents)\n/* harmony export */ });\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Agents() {\n  return /*#__PURE__*/React.createElement(\"main\", {\n    className: \"flex items-center justify-center pt-16 pb-4\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex-1 flex flex-col items-center gap-16 min-h-0\"\n  }, /*#__PURE__*/React.createElement(\"header\", {\n    className: \"flex flex-col items-center gap-9\"\n  }, \"Agents\"), /*#__PURE__*/React.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_0__.Outlet, null)));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm91dGVzL0FnZW50cy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDO0FBRTNCLFNBQVNDLE1BQU1BLENBQUEsRUFBRztFQUMvQixvQkFDRUMsS0FBQSxDQUFBQyxhQUFBO0lBQU1DLFNBQVMsRUFBQztFQUE2QyxnQkFDM0RGLEtBQUEsQ0FBQUMsYUFBQTtJQUFLQyxTQUFTLEVBQUM7RUFBa0QsZ0JBQy9ERixLQUFBLENBQUFDLGFBQUE7SUFBUUMsU0FBUyxFQUFDO0VBQWtDLEdBQUMsUUFBYyxDQUFDLGVBRXBFRixLQUFBLENBQUFDLGFBQUEsQ0FBQ0gsb0RBQU0sTUFBRSxDQUNOLENBQ0QsQ0FBQztBQUVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3JvdXRlcy9BZ2VudHMvaW5kZXgudHN4P2NjYjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgT3V0bGV0IH0gZnJvbSBcInJlYWN0LXJvdXRlci1kb21cIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWdlbnRzKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB0LTE2IHBiLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0xNiBtaW4taC0wXCI+XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTlcIj5BZ2VudHM8L2hlYWRlcj5cblxuICAgICAgICA8T3V0bGV0IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiT3V0bGV0IiwiQWdlbnRzIiwiUmVhY3QiLCJjcmVhdGVFbGVtZW50IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/routes/Agents/index.tsx\n");

/***/ }),

/***/ "./app/routes/Agents/new.tsx":
/*!***********************************!*\
  !*** ./app/routes/Agents/new.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewAgents)\n/* harmony export */ });\nfunction NewAgents() {\n  return /*#__PURE__*/React.createElement(\"main\", {\n    className: \"flex items-center justify-center pt-16 pb-4 bg-red\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex-1 flex flex-col items-center gap-16 min-h-0\"\n  }, /*#__PURE__*/React.createElement(\"header\", {\n    className: \"flex flex-col items-center gap-9\"\n  }, \"New Agents\")));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm91dGVzL0FnZW50cy9uZXcudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxTQUFTQSxDQUFBLEVBQUc7RUFDbEMsb0JBQ0VDLEtBQUEsQ0FBQUMsYUFBQTtJQUFNQyxTQUFTLEVBQUM7RUFBb0QsZ0JBQ2xFRixLQUFBLENBQUFDLGFBQUE7SUFBS0MsU0FBUyxFQUFDO0VBQWtELGdCQUMvREYsS0FBQSxDQUFBQyxhQUFBO0lBQVFDLFNBQVMsRUFBQztFQUFrQyxHQUFDLFlBQWtCLENBQ3BFLENBQ0QsQ0FBQztBQUVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm90LXVpLy4vYXBwL3JvdXRlcy9BZ2VudHMvbmV3LnRzeD85MDdjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5ld0FnZW50cygpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwdC0xNiBwYi00IGJnLXJlZFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTE2IG1pbi1oLTBcIj5cbiAgICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtOVwiPk5ldyBBZ2VudHM8L2hlYWRlcj5cbiAgICAgIDwvZGl2PlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJOZXdBZ2VudHMiLCJSZWFjdCIsImNyZWF0ZUVsZW1lbnQiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./app/routes/Agents/new.tsx\n");

/***/ }),

/***/ "./app/routes/Builder/index.tsx":
/*!**************************************!*\
  !*** ./app/routes/Builder/index.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BuilderPage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_builder_Builder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/builder/Builder */ \"./app/components/builder/Builder.tsx\");\n\n\nfunction BuilderPage() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_builder_Builder__WEBPACK_IMPORTED_MODULE_1__[\"default\"], null);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvcm91dGVzL0J1aWxkZXIvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDNkI7QUFFeEMsU0FBU0UsV0FBV0EsQ0FBQSxFQUFHO0VBQ3BDLG9CQUFPRiwwREFBQSxDQUFDQyxtRUFBTyxNQUFFLENBQUM7QUFDcEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib3QtdWkvLi9hcHAvcm91dGVzL0J1aWxkZXIvaW5kZXgudHN4P2YzY2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IEJ1aWxkZXIgZnJvbSBcIi4uLy4uL2NvbXBvbmVudHMvYnVpbGRlci9CdWlsZGVyXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJ1aWxkZXJQYWdlKCkge1xuICByZXR1cm4gPEJ1aWxkZXIgLz47XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJCdWlsZGVyIiwiQnVpbGRlclBhZ2UiLCJjcmVhdGVFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./app/routes/Builder/index.tsx\n");

/***/ }),

/***/ "./app/routes/Chat/index.tsx":
/*!***********************************!*\
  !*** ./app/routes/Chat/index.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_chat_ChatList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/chat/ChatList */ \"./app/components/chat/ChatList.tsx\");\n/* harmony import */ var _components_chat_ChatWindow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/chat/ChatWindow */ \"./app/components/chat/ChatWindow.tsx\");\n\n\n\nfunction ChatPage() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"h-screen flex bg-gray-50\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_chat_ChatList__WEBPACK_IMPORTED_MODULE_1__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_chat_ChatWindow__WEBPACK_IMPORTED_MODULE_2__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-80 bg-white border-l border-gray-200 flex flex-col\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-b border-gray-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-lg font-semibold text-gray-900\"\n  }, \"Comments\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-8 h-8 text-gray-400\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-lg font-medium text-gray-900 mb-2\"\n  }, \"No comments - yet\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-gray-500 text-sm mb-4\"\n  }, \"Save feedback and comments, or start a\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"br\", null), \"discussion in this conversation.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-gray-400 text-xs\"\n  }, \"All agent comments\")))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/routes/Chat/index.tsx\n");

/***/ }),

/***/ "./app/routes/Settings/index.tsx":
/*!***************************************!*\
  !*** ./app/routes/Settings/index.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/layout/Layout */ \"./app/components/layout/Layout.tsx\");\n/* harmony import */ var _components_settings_SettingsCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/settings/SettingsCard */ \"./app/components/settings/SettingsCard.tsx\");\n\n\n\nfunction SettingsPage() {\n  var settingsCards = [{\n    title: \"Language\",\n    description: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor\",\n    icon: \"🌐\"\n  }, {\n    title: \"NLU\",\n    description: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor\",\n    icon: \"🧠\"\n  }, {\n    title: \"LLM Configuration\",\n    description: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor\",\n    icon: \"⚙️\"\n  }, {\n    title: \"Content Resources\",\n    description: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor\",\n    icon: \"📚\"\n  }, {\n    title: \"Personalization\",\n    description: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor\",\n    icon: \"👤\"\n  }];\n  var headerActions = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors\"\n  }, \"\\uD83D\\uDD0D PREVIEW\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n  }, \"\\uD83D\\uDCE4 PUBLISH\"));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n    title: \"Untitled_25_04_25_0950\",\n    subtitle: \"No description\",\n    headerActions: headerActions\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n  }, settingsCards.map(function (card, index) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_settings_SettingsCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n      key: index,\n      title: card.title,\n      description: card.description,\n      icon: card.icon,\n      onClick: function onClick() {\n        return console.log(\"Clicked \".concat(card.title));\n      }\n    });\n  }))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/routes/Settings/index.tsx\n");

/***/ }),

/***/ "./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./app/app.css":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].use[2]!./app/app.css ***!
  \*********************************************************************************************************************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\nError: It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.\n    at We (/Users/<USER>/Downloads/bot-ui/node_modules/tailwindcss/dist/lib.js:35:2121)\n    at LazyResult.runOnRoot (/Users/<USER>/Downloads/bot-ui/node_modules/postcss/lib/lazy-result.js:361:16)\n    at LazyResult.runAsync (/Users/<USER>/Downloads/bot-ui/node_modules/postcss/lib/lazy-result.js:290:26)\n    at async Object.loader (/Users/<USER>/Downloads/bot-ui/node_modules/postcss-loader/dist/index.js:84:14)");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js":
/*!****************************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nvar stylesInDOM = [];\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n  for (var i = 0; i < stylesInDOM.length; i++) {\n    if (stylesInDOM[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n  return result;\n}\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var indexByIdentifier = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3],\n      supports: item[4],\n      layer: item[5]\n    };\n    if (indexByIdentifier !== -1) {\n      stylesInDOM[indexByIdentifier].references++;\n      stylesInDOM[indexByIdentifier].updater(obj);\n    } else {\n      var updater = addElementStyle(obj, options);\n      options.byIndex = i;\n      stylesInDOM.splice(i, 0, {\n        identifier: identifier,\n        updater: updater,\n        references: 1\n      });\n    }\n    identifiers.push(identifier);\n  }\n  return identifiers;\n}\nfunction addElementStyle(obj, options) {\n  var api = options.domAPI(options);\n  api.update(obj);\n  var updater = function updater(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap && newObj.supports === obj.supports && newObj.layer === obj.layer) {\n        return;\n      }\n      api.update(obj = newObj);\n    } else {\n      api.remove();\n    }\n  };\n  return updater;\n}\nmodule.exports = function (list, options) {\n  options = options || {};\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDOM[index].references--;\n    }\n    var newLastIdentifiers = modulesToDom(newList, options);\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n      var _index = getIndexByIdentifier(_identifier);\n      if (stylesInDOM[_index].references === 0) {\n        stylesInDOM[_index].updater();\n        stylesInDOM.splice(_index, 1);\n      }\n    }\n    lastIdentifiers = newLastIdentifiers;\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\n");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/insertBySelector.js":
/*!********************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/insertBySelector.js ***!
  \********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nvar memo = {};\n\n/* istanbul ignore next  */\nfunction getTarget(target) {\n  if (typeof memo[target] === \"undefined\") {\n    var styleTarget = document.querySelector(target);\n\n    // Special case to return head of iframe instead of iframe itself\n    if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n      try {\n        // This will throw an exception if access to iframe is blocked\n        // due to cross-origin restrictions\n        styleTarget = styleTarget.contentDocument.head;\n      } catch (e) {\n        // istanbul ignore next\n        styleTarget = null;\n      }\n    }\n    memo[target] = styleTarget;\n  }\n  return memo[target];\n}\n\n/* istanbul ignore next  */\nfunction insertBySelector(insert, style) {\n  var target = getTarget(insert);\n  if (!target) {\n    throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n  }\n  target.appendChild(style);\n}\nmodule.exports = insertBySelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRCeVNlbGVjdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydEJ5U2VsZWN0b3IuanM/YjIxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIG1lbW8gPSB7fTtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBnZXRUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0eXBlb2YgbWVtb1t0YXJnZXRdID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgdmFyIHN0eWxlVGFyZ2V0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcih0YXJnZXQpO1xuXG4gICAgLy8gU3BlY2lhbCBjYXNlIHRvIHJldHVybiBoZWFkIG9mIGlmcmFtZSBpbnN0ZWFkIG9mIGlmcmFtZSBpdHNlbGZcbiAgICBpZiAod2luZG93LkhUTUxJRnJhbWVFbGVtZW50ICYmIHN0eWxlVGFyZ2V0IGluc3RhbmNlb2Ygd2luZG93LkhUTUxJRnJhbWVFbGVtZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICAvLyBUaGlzIHdpbGwgdGhyb3cgYW4gZXhjZXB0aW9uIGlmIGFjY2VzcyB0byBpZnJhbWUgaXMgYmxvY2tlZFxuICAgICAgICAvLyBkdWUgdG8gY3Jvc3Mtb3JpZ2luIHJlc3RyaWN0aW9uc1xuICAgICAgICBzdHlsZVRhcmdldCA9IHN0eWxlVGFyZ2V0LmNvbnRlbnREb2N1bWVudC5oZWFkO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAvLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuICAgICAgICBzdHlsZVRhcmdldCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG1lbW9bdGFyZ2V0XSA9IHN0eWxlVGFyZ2V0O1xuICB9XG4gIHJldHVybiBtZW1vW3RhcmdldF07XG59XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gaW5zZXJ0QnlTZWxlY3RvcihpbnNlcnQsIHN0eWxlKSB7XG4gIHZhciB0YXJnZXQgPSBnZXRUYXJnZXQoaW5zZXJ0KTtcbiAgaWYgKCF0YXJnZXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBmaW5kIGEgc3R5bGUgdGFyZ2V0LiBUaGlzIHByb2JhYmx5IG1lYW5zIHRoYXQgdGhlIHZhbHVlIGZvciB0aGUgJ2luc2VydCcgcGFyYW1ldGVyIGlzIGludmFsaWQuXCIpO1xuICB9XG4gIHRhcmdldC5hcHBlbmRDaGlsZChzdHlsZSk7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydEJ5U2VsZWN0b3I7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/insertBySelector.js\n");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/insertStyleElement.js":
/*!**********************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/insertStyleElement.js ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/* istanbul ignore next  */\nfunction insertStyleElement(options) {\n  var element = document.createElement(\"style\");\n  options.setAttributes(element, options.attributes);\n  options.insert(element, options.options);\n  return element;\n}\nmodule.exports = insertStyleElement;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9pbnNlcnRTdHlsZUVsZW1lbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL2luc2VydFN0eWxlRWxlbWVudC5qcz9kZTZjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG4vKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAgKi9cbmZ1bmN0aW9uIGluc2VydFN0eWxlRWxlbWVudChvcHRpb25zKSB7XG4gIHZhciBlbGVtZW50ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIpO1xuICBvcHRpb25zLnNldEF0dHJpYnV0ZXMoZWxlbWVudCwgb3B0aW9ucy5hdHRyaWJ1dGVzKTtcbiAgb3B0aW9ucy5pbnNlcnQoZWxlbWVudCwgb3B0aW9ucy5vcHRpb25zKTtcbiAgcmV0dXJuIGVsZW1lbnQ7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGluc2VydFN0eWxlRWxlbWVudDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/insertStyleElement.js\n");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n/* istanbul ignore next  */\nfunction setAttributesWithoutAttributes(styleElement) {\n  var nonce =  true ? __webpack_require__.nc : 0;\n  if (nonce) {\n    styleElement.setAttribute(\"nonce\", nonce);\n  }\n}\nmodule.exports = setAttributesWithoutAttributes;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQTtBQUNBLGNBQWMsS0FBd0MsR0FBRyxzQkFBaUIsR0FBRyxDQUFJO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib3QtdWkvLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMuanM/ZGRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyogaXN0YW5idWwgaWdub3JlIG5leHQgICovXG5mdW5jdGlvbiBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXMoc3R5bGVFbGVtZW50KSB7XG4gIHZhciBub25jZSA9IHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gXCJ1bmRlZmluZWRcIiA/IF9fd2VicGFja19ub25jZV9fIDogbnVsbDtcbiAgaWYgKG5vbmNlKSB7XG4gICAgc3R5bGVFbGVtZW50LnNldEF0dHJpYnV0ZShcIm5vbmNlXCIsIG5vbmNlKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzZXRBdHRyaWJ1dGVzV2l0aG91dEF0dHJpYnV0ZXM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\n");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/styleDomAPI.js":
/*!***************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/styleDomAPI.js ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/* istanbul ignore next  */\nfunction apply(styleElement, options, obj) {\n  var css = \"\";\n  if (obj.supports) {\n    css += \"@supports (\".concat(obj.supports, \") {\");\n  }\n  if (obj.media) {\n    css += \"@media \".concat(obj.media, \" {\");\n  }\n  var needLayer = typeof obj.layer !== \"undefined\";\n  if (needLayer) {\n    css += \"@layer\".concat(obj.layer.length > 0 ? \" \".concat(obj.layer) : \"\", \" {\");\n  }\n  css += obj.css;\n  if (needLayer) {\n    css += \"}\";\n  }\n  if (obj.media) {\n    css += \"}\";\n  }\n  if (obj.supports) {\n    css += \"}\";\n  }\n  var sourceMap = obj.sourceMap;\n  if (sourceMap && typeof btoa !== \"undefined\") {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  }\n\n  // For old IE\n  /* istanbul ignore if  */\n  options.styleTagTransform(css, styleElement, options.options);\n}\nfunction removeStyleElement(styleElement) {\n  // istanbul ignore if\n  if (styleElement.parentNode === null) {\n    return false;\n  }\n  styleElement.parentNode.removeChild(styleElement);\n}\n\n/* istanbul ignore next  */\nfunction domAPI(options) {\n  if (typeof document === \"undefined\") {\n    return {\n      update: function update() {},\n      remove: function remove() {}\n    };\n  }\n  var styleElement = options.insertStyleElement(options);\n  return {\n    update: function update(obj) {\n      apply(styleElement, options, obj);\n    },\n    remove: function remove() {\n      removeStyleElement(styleElement);\n    }\n  };\n}\nmodule.exports = domAPI;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/styleDomAPI.js\n");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/styleTagTransform.js":
/*!*********************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/styleTagTransform.js ***!
  \*********************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\n/* istanbul ignore next  */\nfunction styleTagTransform(css, styleElement) {\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css;\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild);\n    }\n    styleElement.appendChild(document.createTextNode(css));\n  }\n}\nmodule.exports = styleTagTransform;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvc3R5bGUtbG9hZGVyL2Rpc3QvcnVudGltZS9zdHlsZVRhZ1RyYW5zZm9ybS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JvdC11aS8uL25vZGVfbW9kdWxlcy9zdHlsZS1sb2FkZXIvZGlzdC9ydW50aW1lL3N0eWxlVGFnVHJhbnNmb3JtLmpzPzFkZGUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICAqL1xuZnVuY3Rpb24gc3R5bGVUYWdUcmFuc2Zvcm0oY3NzLCBzdHlsZUVsZW1lbnQpIHtcbiAgaWYgKHN0eWxlRWxlbWVudC5zdHlsZVNoZWV0KSB7XG4gICAgc3R5bGVFbGVtZW50LnN0eWxlU2hlZXQuY3NzVGV4dCA9IGNzcztcbiAgfSBlbHNlIHtcbiAgICB3aGlsZSAoc3R5bGVFbGVtZW50LmZpcnN0Q2hpbGQpIHtcbiAgICAgIHN0eWxlRWxlbWVudC5yZW1vdmVDaGlsZChzdHlsZUVsZW1lbnQuZmlyc3RDaGlsZCk7XG4gICAgfVxuICAgIHN0eWxlRWxlbWVudC5hcHBlbmRDaGlsZChkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShjc3MpKTtcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBzdHlsZVRhZ1RyYW5zZm9ybTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/style-loader/dist/runtime/styleTagTransform.js\n");

/***/ })

}]);