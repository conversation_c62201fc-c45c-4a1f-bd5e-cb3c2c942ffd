"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_components_MicroFrontendDemo_tsx"],{

/***/ "./app/components/MicroFrontendDemo.tsx":
/*!**********************************************!*\
  !*** ./app/components/MicroFrontendDemo.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MicroFrontendDemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\n\n\n/**\n * A simple demo component to verify that the micro-frontend integration is working correctly.\n * This component includes:\n * - State management with React hooks\n * - Styling with Tailwind CSS\n * - Interactive elements\n */\nfunction MicroFrontendDemo() {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    count = _useState2[0],\n    setCount = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"Hello from Bot UI Micro-Frontend!\"),\n    _useState4 = _slicedToArray(_useState3, 2),\n    message = _useState4[0],\n    setMessage = _useState4[1];\n  var incrementCount = function incrementCount() {\n    setCount(count + 1);\n  };\n  var changeMessage = function changeMessage() {\n    var messages = [\"Hello from Bot UI Micro-Frontend!\", \"Integration successful!\", \"Module Federation is working!\", \"You can now use all components from this micro-frontend!\", \"Congratulations on setting up your micro-frontend architecture!\"];\n    var randomIndex = Math.floor(Math.random() * messages.length);\n    setMessage(messages[randomIndex]);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"p-6 max-w-md mx-auto bg-white rounded-xl shadow-md flex flex-col items-center space-y-4 mt-4\"\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"text-center\"\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    className: \"text-2xl font-bold text-blue-600\"\n  }, \"Micro-Frontend Demo\"), /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-gray-500 mt-2\"\n  }, \"This component is loaded from the Bot UI micro-frontend\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"bg-blue-100 p-4 rounded-lg w-full text-center\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-lg font-semibold\"\n  }, message)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex flex-col items-center space-y-2 w-full\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-gray-700\"\n  }, \"Counter: \", /*#__PURE__*/React.createElement(\"span\", {\n    className: \"font-bold\"\n  }, count)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"flex space-x-2\"\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: incrementCount,\n    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\"\n  }, \"Increment\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: changeMessage,\n    className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors\"\n  }, \"Change Message\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"border-t border-gray-200 pt-4 w-full\"\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    className: \"text-sm text-gray-500 text-center\"\n  }, \"Current time: \", new Date().toLocaleTimeString())));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/MicroFrontendDemo.tsx\n");

/***/ })

}]);