"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkbot_ui"] = self["webpackChunkbot_ui"] || []).push([["app_routes_Home_index_tsx"],{

/***/ "./app/components/dashboard/ChatbotCard.tsx":
/*!**************************************************!*\
  !*** ./app/components/dashboard/ChatbotCard.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar ChatbotCard = function ChatbotCard(_ref) {\n  var chatbot = _ref.chatbot,\n    onEdit = _ref.onEdit,\n    onDelete = _ref.onDelete;\n  var statusConfig = chatbot.status === \"live\" ? {\n    bgColor: \"bg-emerald-500\",\n    textColor: \"text-white\",\n    dotColor: \"bg-white\",\n    text: \"Live\"\n  } : {\n    bgColor: \"bg-gray-400\",\n    textColor: \"text-white\",\n    dotColor: \"bg-white\",\n    text: \"Draft\"\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white rounded-[12px] border border-[#e2e8f0] p-4 hover:shadow-[0_8px_25px_rgba(0,0,0,0.1)] hover:border-[#cbd5e1] transition-all duration-200 group\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-start justify-between mb-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-10 h-10 bg-[#f1f5f9] rounded-[8px] flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-7 h-7 bg-[#64748b] rounded-[5px] flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-4 h-4 bg-white rounded-[2px] flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-1.5 h-1.5 bg-[#64748b] rounded-full\"\n  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"px-3 py-1 rounded-full text-xs font-medium \".concat(statusConfig.bgColor, \" \").concat(statusConfig.textColor)\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-1.5 h-1.5 \".concat(statusConfig.dotColor, \" rounded-full\")\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, statusConfig.text)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mb-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"text-base font-semibold text-[#1e293b] mb-1.5 group-hover:text-[#0f172a] transition-colors\"\n  }, chatbot.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-sm text-[#64748b] leading-relaxed line-clamp-2\"\n  }, chatbot.description)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between pt-3 border-t border-[#f1f5f9]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-[#94a3b8] font-medium\"\n  }, \"Last updated \", chatbot.lastUpdated), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Link, {\n    to: \"/builder/\".concat(chatbot.id),\n    className: \"p-1.5 text-[#94a3b8] hover:text-[#3b82f6] hover:bg-[#eff6ff] rounded-md transition-all duration-200\",\n    title: \"Edit\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: function onClick() {\n      return onDelete === null || onDelete === void 0 ? void 0 : onDelete(chatbot.id);\n    },\n    className: \"p-1.5 text-[#94a3b8] hover:text-[#ef4444] hover:bg-[#fef2f2] rounded-md transition-all duration-200\",\n    title: \"Delete\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-1.5 text-[#94a3b8] hover:text-[#64748b] hover:bg-[#f8fafc] rounded-md transition-all duration-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"\n  }))))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatbotCard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/dashboard/ChatbotCard.tsx\n");

/***/ }),

/***/ "./app/components/dashboard/ChatbotGrid.tsx":
/*!**************************************************!*\
  !*** ./app/components/dashboard/ChatbotGrid.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_chatbots_chatbotsSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/chatbots/chatbotsSlice */ \"./app/redux/chatbots/chatbotsSlice.ts\");\n/* harmony import */ var _ChatbotCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChatbotCard */ \"./app/components/dashboard/ChatbotCard.tsx\");\n\n\n\n\nvar ChatbotGrid = function ChatbotGrid() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.chatbots;\n    }),\n    chatbots = _useAppSelector.chatbots,\n    searchQuery = _useAppSelector.searchQuery,\n    statusFilter = _useAppSelector.statusFilter,\n    loading = _useAppSelector.loading;\n\n  // Filter chatbots based on search query and status filter\n  var filteredChatbots = chatbots.filter(function (chatbot) {\n    var matchesSearch = chatbot.name.toLowerCase().includes(searchQuery.toLowerCase()) || chatbot.description.toLowerCase().includes(searchQuery.toLowerCase());\n    var matchesStatus = statusFilter === \"all\" || chatbot.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n  var handleDelete = function handleDelete(id) {\n    if (window.confirm(\"Are you sure you want to delete this chatbot?\")) {\n      dispatch((0,_redux_chatbots_chatbotsSlice__WEBPACK_IMPORTED_MODULE_2__.deleteChatbot)(id));\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"flex items-center justify-center h-64\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-600\"\n    }));\n  }\n  if (filteredChatbots.length === 0) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"text-center py-12\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n      className: \"w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n      className: \"w-8 h-8 text-gray-400\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      strokeWidth: 2,\n      d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n      className: \"text-lg font-medium text-gray-900 mb-2\"\n    }, \"No chatbots found\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n      className: \"text-gray-500 mb-4\"\n    }, searchQuery || statusFilter !== \"all\" ? \"Try adjusting your search or filters to find what you're looking for.\" : \"Get started by creating your first chatbot.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n      className: \"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors\"\n    }, \"Create Chatbot\"));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5\"\n  }, filteredChatbots.map(function (chatbot) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_ChatbotCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n      key: chatbot.id,\n      chatbot: chatbot,\n      onDelete: handleDelete\n    });\n  }));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatbotGrid);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/dashboard/ChatbotGrid.tsx\n");

/***/ }),

/***/ "./app/components/dashboard/SearchAndFilters.tsx":
/*!*******************************************************!*\
  !*** ./app/components/dashboard/SearchAndFilters.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/redux-hooks */ \"./app/hooks/redux-hooks.ts\");\n/* harmony import */ var _redux_chatbots_chatbotsSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../redux/chatbots/chatbotsSlice */ \"./app/redux/chatbots/chatbotsSlice.ts\");\n\n\n\nvar SearchAndFilters = function SearchAndFilters() {\n  var dispatch = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n  var _useAppSelector = (0,_hooks_redux_hooks__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)(function (state) {\n      return state.chatbots;\n    }),\n    searchQuery = _useAppSelector.searchQuery,\n    statusFilter = _useAppSelector.statusFilter;\n  var handleSearchChange = function handleSearchChange(e) {\n    dispatch((0,_redux_chatbots_chatbotsSlice__WEBPACK_IMPORTED_MODULE_2__.setSearchQuery)(e.target.value));\n  };\n  var handleStatusFilterChange = function handleStatusFilterChange(status) {\n    dispatch((0,_redux_chatbots_chatbotsSlice__WEBPACK_IMPORTED_MODULE_2__.setStatusFilter)(status));\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center justify-between mb-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 max-w-[320px]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"h-4 w-4 text-[#94a3b8]\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"Search\",\n    value: searchQuery,\n    onChange: handleSearchChange,\n    className: \"block w-full pl-9 pr-4 py-2.5 border border-[#e2e8f0] rounded-[8px] leading-5 bg-white placeholder-[#94a3b8] focus:outline-none focus:placeholder-[#64748b] focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] text-sm\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"select\", {\n    className: \"appearance-none bg-white border border-[#e2e8f0] rounded-[8px] px-4 py-2.5 pr-10 text-sm font-medium focus:outline-none focus:ring-1 focus:ring-[#3b82f6] focus:border-[#3b82f6] shadow-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"option\", null, \"Last 30 Days\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"option\", null, \"Last 7 Days\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"option\", null, \"Last 24 Hours\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"option\", null, \"All Time\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4 text-[#94a3b8]\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M19 9l-7 7-7-7\"\n  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2 bg-gray-50 rounded-xl p-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: function onClick() {\n      return handleStatusFilterChange(\"all\");\n    },\n    className: \"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 \".concat(statusFilter === \"all\" ? \"bg-white text-gray-900 shadow-sm\" : \"text-gray-600 hover:text-gray-900 hover:bg-white/50\")\n  }, \"All\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: function onClick() {\n      return handleStatusFilterChange(\"live\");\n    },\n    className: \"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 \".concat(statusFilter === \"live\" ? \"bg-white text-gray-900 shadow-sm\" : \"text-gray-600 hover:text-gray-900 hover:bg-white/50\")\n  }, \"Live\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    onClick: function onClick() {\n      return handleStatusFilterChange(\"draft\");\n    },\n    className: \"px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 \".concat(statusFilter === \"draft\" ? \"bg-white text-gray-900 shadow-sm\" : \"text-gray-600 hover:text-gray-900 hover:bg-white/50\")\n  }, \"Draft\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"flex items-center space-x-2 px-4 py-2.5 border border-[#e2e8f0] rounded-[8px] text-sm font-medium text-[#64748b] hover:bg-[#f8fafc] transition-all duration-200 shadow-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, \"Filter\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center border border-[#e2e8f0] rounded-[8px] overflow-hidden shadow-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2.5 text-[#64748b] hover:text-[#1e293b] bg-white hover:bg-[#f8fafc] transition-all duration-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"p-2.5 text-[#94a3b8] hover:text-[#64748b] bg-[#f8fafc] hover:bg-[#f1f5f9] transition-all duration-200\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    strokeWidth: 2,\n    d: \"M4 6h16M4 10h16M4 14h16M4 18h16\"\n  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"bg-[#EF4444] hover:bg-[#DC2626] text-white px-6 py-2.5 rounded-[8px] text-sm font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105\"\n  }, \"CREATE\")));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchAndFilters);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/dashboard/SearchAndFilters.tsx\n");

/***/ }),

/***/ "./app/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./app/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar Header = function Header(_ref) {\n  var _ref$title = _ref.title,\n    title = _ref$title === void 0 ? \"NeuraTalk AI\" : _ref$title,\n    _ref$subtitle = _ref.subtitle,\n    subtitle = _ref$subtitle === void 0 ? \"NeuraTalk AI is a cutting-edge conversational AI solution designed to enhance customer engagement, automate support, and streamline business operations.\" : _ref$subtitle,\n    actions = _ref.actions;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-white border-b border-[#e2e8f0] px-6 py-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-start justify-between\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-start space-x-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h1\", {\n    className: \"text-2xl font-bold text-[#1e293b] mb-2\"\n  }, title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-sm text-[#64748b] leading-relaxed max-w-3xl\"\n  }, subtitle)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-shrink-0\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-16 h-16 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center shadow-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-gradient-to-br from-[#f97316] to-[#ea580c] rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-5 h-5 text-white\",\n    fill: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21ZM14 15.5L22.5 7L21 5.5L14 12.5L10.5 9L9 10.5L14 15.5Z\"\n  })))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-4 ml-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2 px-2 py-1 bg-[#f8fafc] rounded-md\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-sm\"\n  }, \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-xs font-medium text-[#64748b]\"\n  }, \"EN\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-[#e2e8f0] rounded-full flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"svg\", {\n    className: \"w-4 h-4 text-[#64748b]\",\n    fill: \"currentColor\",\n    viewBox: \"0 0 24 24\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"path\", {\n    d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n  })))), actions)));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/layout/Header.tsx\n");

/***/ }),

/***/ "./app/components/layout/Layout.tsx":
/*!******************************************!*\
  !*** ./app/components/layout/Layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"./app/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"./app/components/layout/Header.tsx\");\n\n\n\nvar Layout = function Layout(_ref) {\n  var children = _ref.children,\n    title = _ref.title,\n    subtitle = _ref.subtitle,\n    headerActions = _ref.headerActions,\n    _ref$sidebarCollapsed = _ref.sidebarCollapsed,\n    sidebarCollapsed = _ref$sidebarCollapsed === void 0 ? false : _ref$sidebarCollapsed;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex h-screen bg-white\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n    collapsed: sidebarCollapsed\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex-1 flex flex-col overflow-hidden\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    title: title,\n    subtitle: subtitle,\n    actions: headerActions\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"main\", {\n    className: \"flex-1 overflow-auto\"\n  }, children)));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/layout/Layout.tsx\n");

/***/ }),

/***/ "./app/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./app/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-router-dom */ \"webpack/sharing/consume/default/react-router-dom/react-router-dom\");\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_router_dom__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar Sidebar = function Sidebar(_ref) {\n  var _ref$collapsed = _ref.collapsed,\n    collapsed = _ref$collapsed === void 0 ? false : _ref$collapsed;\n  var location = (0,react_router_dom__WEBPACK_IMPORTED_MODULE_1__.useLocation)();\n  var menuItems = [{\n    icon: \"🏠\",\n    label: \"Home\",\n    path: \"/\",\n    id: \"home\"\n  }, {\n    icon: \"📊\",\n    label: \"Insights\",\n    path: \"/insights\",\n    id: \"insights\"\n  }, {\n    icon: \"#\",\n    label: \"Assets\",\n    path: \"/assets\",\n    id: \"assets\"\n  }, {\n    icon: \"🔍\",\n    label: \"Reach\",\n    path: \"/reach\",\n    id: \"reach\"\n  }, {\n    icon: \"📁\",\n    label: \"Directory\",\n    path: \"/directory\",\n    id: \"directory\"\n  }, {\n    icon: \"🎨\",\n    label: \"Studio\",\n    path: \"/studio\",\n    id: \"studio\"\n  }, {\n    icon: \"💬\",\n    label: \"Halo\",\n    path: \"/halo\",\n    id: \"halo\"\n  }, {\n    icon: \"🤖\",\n    label: \"NeuraTalk AI\",\n    path: \"/\",\n    id: \"neuratalk\",\n    active: true\n  }, {\n    icon: \"📋\",\n    label: \"Plans\",\n    path: \"/plans\",\n    id: \"plans\"\n  }, {\n    icon: \"📝\",\n    label: \"Logs\",\n    path: \"/logs\",\n    id: \"logs\"\n  }, {\n    icon: \"👥\",\n    label: \"Developers\",\n    path: \"/developers\",\n    id: \"developers\"\n  }];\n  var isActive = function isActive(path) {\n    if (path === \"/\") {\n      return location.pathname === \"/\";\n    }\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"bg-[#2D3748] text-white h-screen flex flex-col transition-all duration-300 \".concat(collapsed ? \"w-16\" : \"w-64\")\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-b border-[#4A5568]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-white rounded flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-[#2D3748] font-bold text-sm\"\n  }, \"C\")), !collapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-xl font-bold\"\n  }, \"comviva\"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-b border-[#4A5568]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-8 h-8 bg-[#4A5568] rounded-full flex items-center justify-center relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-sm text-white\"\n  }, \"JD\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-white text-xs\"\n  }, \"1\"))), !collapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-sm font-medium text-white\"\n  }, \"John Doe\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-gray-300\"\n  }, \"Bot $ 10K\")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"nav\", {\n    className: \"flex-1 py-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"ul\", {\n    className: \"space-y-1\"\n  }, menuItems.map(function (item) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"li\", {\n      key: item.id\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__.Link, {\n      to: item.path,\n      className: \"flex items-center px-4 py-3 text-sm transition-colors duration-200 \".concat(item.active || isActive(item.path) ? \"bg-[#EF4444] text-white border-r-4 border-[#DC2626]\" : \"text-gray-300 hover:bg-[#4A5568] hover:text-white\")\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n      className: \"text-lg mr-3\"\n    }, item.icon), !collapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", null, item.label)));\n  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 border-t border-[#4A5568]\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"flex items-center space-x-2\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"w-6 h-6 bg-[#4A5568] rounded flex items-center justify-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", {\n    className: \"text-xs text-white\"\n  }, \"C+\")), !collapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-gray-300\"\n  }, \"Logout\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"text-xs text-gray-400\"\n  }, \"2023 \\xA9 Comviva Technologies Limited\")))));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "./app/hooks/redux-hooks.ts":
/*!**********************************!*\
  !*** ./app/hooks/redux-hooks.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-redux */ \"webpack/sharing/consume/default/react-redux/react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_0__);\n// Typed hooks\n\n// Recommended to use these instead of plain 'useDispatch' and 'useSelector'\nvar useAppDispatch = react_redux__WEBPACK_IMPORTED_MODULE_0__.useDispatch;\nvar useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_0__.useSelector;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hcHAvaG9va3MvcmVkdXgtaG9va3MudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ3FEO0FBSXJEO0FBQ08sSUFBTUUsY0FBaUMsR0FBR0Ysb0RBQVc7QUFDckQsSUFBTUcsY0FBK0MsR0FBR0Ysb0RBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib3QtdWkvLi9hcHAvaG9va3MvcmVkdXgtaG9va3MudHM/OGVhOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUeXBlZCBob29rc1xuaW1wb3J0IHt1c2VEaXNwYXRjaCwgdXNlU2VsZWN0b3J9IGZyb20gJ3JlYWN0LXJlZHV4JztcbmltcG9ydCB0eXBlIHtUeXBlZFVzZVNlbGVjdG9ySG9va30gZnJvbSAncmVhY3QtcmVkdXgnO1xuaW1wb3J0IHR5cGUgeyBBcHBEaXNwYXRjaCwgUm9vdFN0YXRlIH0gZnJvbSAnfi9yZWR1eC9zdG9yZSc7XG5cbi8vIFJlY29tbWVuZGVkIHRvIHVzZSB0aGVzZSBpbnN0ZWFkIG9mIHBsYWluICd1c2VEaXNwYXRjaCcgYW5kICd1c2VTZWxlY3RvcidcbmV4cG9ydCBjb25zdCB1c2VBcHBEaXNwYXRjaDogKCkgPT4gQXBwRGlzcGF0Y2ggPSB1c2VEaXNwYXRjaDtcbmV4cG9ydCBjb25zdCB1c2VBcHBTZWxlY3RvcjogVHlwZWRVc2VTZWxlY3Rvckhvb2s8Um9vdFN0YXRlPiA9IHVzZVNlbGVjdG9yOyJdLCJuYW1lcyI6WyJ1c2VEaXNwYXRjaCIsInVzZVNlbGVjdG9yIiwidXNlQXBwRGlzcGF0Y2giLCJ1c2VBcHBTZWxlY3RvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./app/hooks/redux-hooks.ts\n");

/***/ }),

/***/ "./app/routes/Home/index.tsx":
/*!***********************************!*\
  !*** ./app/routes/Home/index.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/layout/Layout */ \"./app/components/layout/Layout.tsx\");\n/* harmony import */ var _components_dashboard_SearchAndFilters__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/dashboard/SearchAndFilters */ \"./app/components/dashboard/SearchAndFilters.tsx\");\n/* harmony import */ var _components_dashboard_ChatbotGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/dashboard/ChatbotGrid */ \"./app/components/dashboard/ChatbotGrid.tsx\");\n\n\n\n\nfunction Home() {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"min-h-screen bg-[#fafbfc] p-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"max-w-[1400px] mx-auto\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mb-8 p-6 bg-white rounded-lg shadow-md border\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h2\", {\n    className: \"text-2xl font-bold text-gray-800 mb-4\"\n  }, \"\\uD83C\\uDFA8 Tailwind CSS Status\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 bg-green-100 border border-green-300 rounded-lg\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"font-semibold text-green-800\"\n  }, \"\\u2705 Colors Working\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-green-600 text-sm\"\n  }, \"If you see green styling, colors are working!\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 bg-blue-100 border border-blue-300 rounded-lg\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"font-semibold text-blue-800\"\n  }, \"\\u2705 Layout Working\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-blue-600 text-sm\"\n  }, \"Grid and spacing utilities are functional!\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"p-4 bg-purple-100 border border-purple-300 rounded-lg\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"h3\", {\n    className: \"font-semibold text-purple-800\"\n  }, \"\\u2705 Components Working\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"p\", {\n    className: \"text-purple-600 text-sm\"\n  }, \"Rounded corners and shadows are applied!\"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-4 flex space-x-3\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-200\"\n  }, \"Primary Button\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"button\", {\n    className: \"px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors duration-200\"\n  }, \"Secondary Button\"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_dashboard_SearchAndFilters__WEBPACK_IMPORTED_MODULE_2__[\"default\"], null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    className: \"mt-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_dashboard_ChatbotGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], null)))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./app/routes/Home/index.tsx\n");

/***/ })

}]);