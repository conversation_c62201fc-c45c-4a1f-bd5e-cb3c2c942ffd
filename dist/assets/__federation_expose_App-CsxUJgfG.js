import { importShared } from './__federation_fn_import-CJyKoOdj.js';
import { j as jsxRuntimeExports } from './jsx-runtime-CvJTHeKY.js';
import { h as hydrateAuth, g as setMode, i as setCurrentFlow, j as setSelectedNode, k as updateNode, l as addNode, m as setStatusFilter, n as setSearchQuery, o as setActiveSession, p as updateSessionStatus, q as addMessage, store } from './__federation_expose_Store-krOtjX4R.js';
import Home, { u as useAppDispatch, a as useAppSelector, L as Layout$1 } from './__federation_expose_Home-Db8wbg1o.js';
import Agents from './__federation_expose_Agents-h6Zc5M1N.js';
import NewAgents from './__federation_expose_NewAgent-Dc-UYM85.js';

const {useEffect} = await importShared('react');
function AuthHydrator() {
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (typeof window !== "undefined") {
      dispatch(hydrateAuth());
    }
  }, [dispatch]);
  return null;
}

await importShared('react');
const BuilderTabs = () => {
  const dispatch = useAppDispatch();
  const { mode } = useAppSelector((state) => state.builder);
  const tabs = [
    { id: "design", label: "Design" },
    { id: "train", label: "Train" },
    { id: "channels", label: "Channels" },
    { id: "agent-transfer", label: "Agent Transfer" },
    { id: "integrations", label: "Integrations" },
    { id: "settings", label: "Settings" }
  ];
  const handleTabClick = (tabId) => {
    dispatch(setMode(tabId));
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-[#f8fafc] border-b border-[#e2e8f0]", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex px-6", children: tabs.map((tab) => /* @__PURE__ */ jsxRuntimeExports.jsx(
    "button",
    {
      onClick: () => handleTabClick(tab.id),
      className: `
              px-4 py-3 text-sm font-medium border-b-2 transition-all duration-200 relative
              ${mode === tab.id ? "border-[#3b82f6] text-[#3b82f6] font-semibold bg-white" : "border-transparent text-[#64748b] hover:text-[#1e293b] hover:bg-white/50"}
            `,
      style: mode === tab.id ? {
        borderRadius: "6px 6px 0 0",
        marginBottom: "-1px"
      } : {},
      children: tab.label
    },
    tab.id
  )) }) });
};

await importShared('react');

const BuilderHeader = () => {
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "bg-white border-b border-[#e2e8f0] px-6 py-3", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-6", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 text-sm", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-[#64748b] font-medium", children: "NeuraTalk" }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3 h-3 text-[#cbd5e1]",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M9 5l7 7-7 7"
              }
            )
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-[#3b82f6] font-medium", children: "Create" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-[#f1f5f9] rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4 text-[#64748b]",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              }
            )
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { className: "text-base font-semibold text-[#1e293b]", children: "My Chatbot" }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-3.5 h-3.5",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  }
                )
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-[#64748b] mt-0.5", children: "Help customers navigate the digital purchasing process." })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("button", { className: "flex items-center space-x-2 px-4 py-2 text-sm font-medium text-[#64748b] bg-white border border-[#d1d5db] rounded-lg hover:bg-[#f9fafb] transition-colors", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "path",
                {
                  strokeLinecap: "round",
                  strokeLinejoin: "round",
                  strokeWidth: 2,
                  d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                }
              ),
              /* @__PURE__ */ jsxRuntimeExports.jsx(
                "path",
                {
                  strokeLinecap: "round",
                  strokeLinejoin: "round",
                  strokeWidth: 2,
                  d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                }
              )
            ]
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "PREVIEW" })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("button", { className: "flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-[#3b82f6] rounded-lg hover:bg-[#2563eb] transition-colors", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-4 h-4",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              }
            )
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: "PUBLISH" })
      ] })
    ] })
  ] }) });
};

await importShared('react');

const LeftNavigation = () => {
  const navigationItems = [
    {
      id: "home",
      icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" }) }),
      active: false
    },
    {
      id: "chat",
      icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" }) }),
      active: true
    },
    {
      id: "flows",
      icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M13 10V3L4 14h7v7l9-11h-7z" }) }),
      active: false
    },
    {
      id: "analytics",
      icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z" }) }),
      active: false
    },
    {
      id: "settings",
      icon: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" }) }),
      active: false
    }
  ];
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-[52px] bg-[#334155] flex flex-col items-center py-4 border-r border-[#475569]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-[#3b82f6] rounded-lg flex items-center justify-center mb-6", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5 text-white", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" }) }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex flex-col space-y-2", children: navigationItems.map((item) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        className: `
              w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200
              ${item.active ? "bg-[#3b82f6] text-white shadow-lg" : "text-[#94a3b8] hover:text-white hover:bg-[#475569]"}
            `,
        children: item.icon
      },
      item.id
    )) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-auto", children: /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "w-10 h-10 rounded-lg flex items-center justify-center text-[#94a3b8] hover:text-white hover:bg-[#475569] transition-all duration-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" }) }) }) })
  ] });
};

await importShared('react');
const FlowSidebar = () => {
  const dispatch = useAppDispatch();
  const { flows, currentFlow } = useAppSelector((state) => state.builder);
  const handleFlowSelect = (flowId) => {
    const flow = flows.find((f) => f.id === flowId);
    if (flow) {
      dispatch(setCurrentFlow(flow));
    }
  };
  const addNewFlow = () => {
    console.log("Add new flow");
  };
  const defaultFlows = [
    { id: "welcome", name: "Welcome", status: "Default" },
    { id: "fallback", name: "Fallback", status: "Default" }
  ];
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-[240px] p-4 flex flex-col", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white rounded-xl shadow-lg border border-[#e2e8f0] flex flex-col h-full", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "px-4 py-3 border-b border-[#f1f5f9]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-semibold text-[#1e293b]", children: "Flows" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "button",
        {
          onClick: addNewFlow,
          className: "w-6 h-6 bg-[#3b82f6] text-white rounded-lg flex items-center justify-center hover:bg-[#2563eb] transition-colors shadow-sm",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-3 h-3",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                "path",
                {
                  strokeLinecap: "round",
                  strokeLinejoin: "round",
                  strokeWidth: 2,
                  d: "M12 4v16m8-8H4"
                }
              )
            }
          )
        }
      )
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto p-3", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "space-y-2", children: defaultFlows.map((flow) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        className: `flex items-center justify-between px-3 py-3 rounded-lg cursor-pointer transition-all duration-200 ${currentFlow?.id === flow.id ? "bg-[#eff6ff] border border-[#dbeafe] shadow-sm" : "hover:bg-[#f8fafc] hover:shadow-sm"}`,
        onClick: () => handleFlowSelect(flow.id),
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-[#f1f5f9] rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "svg",
              {
                className: "w-4 h-4 text-[#64748b]",
                fill: "none",
                stroke: "currentColor",
                viewBox: "0 0 24 24",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "path",
                  {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                  }
                )
              }
            ) }),
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-sm font-medium text-[#1e293b]", children: flow.name }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-[#64748b]", children: flow.status })
            ] })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4",
              fill: "none",
              stroke: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx(
                "path",
                {
                  strokeLinecap: "round",
                  strokeLinejoin: "round",
                  strokeWidth: 2,
                  d: "M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                }
              )
            }
          ) })
        ]
      },
      flow.id
    )) }) })
  ] }) });
};

await importShared('react');
const NodeComponent = ({
  node,
  isSelected,
  onClick,
  onDragStart
}) => {
  const getNodeIcon = (type) => {
    switch (type) {
      case "start":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M8 5v14l11-7z" })
          }
        );
      case "message":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" })
          }
        );
      case "interactive":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" })
          }
        );
      case "feedback":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" })
          }
        );
      case "notification":
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" })
          }
        );
      default:
        return /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-3.5 h-3.5 text-white",
            fill: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" })
          }
        );
    }
  };
  const getNodeColor = (type) => {
    switch (type) {
      case "start":
        return "bg-white border-[#10b981] shadow-sm";
      case "message":
        return "bg-white border-[#3b82f6] shadow-sm";
      case "interactive":
        return "bg-white border-[#10b981] shadow-sm";
      case "feedback":
        return "bg-white border-[#f59e0b] shadow-sm";
      case "notification":
        return "bg-white border-[#8b5cf6] shadow-sm";
      default:
        return "bg-white border-[#6b7280] shadow-sm";
    }
  };
  const getIconBgColor = (type) => {
    switch (type) {
      case "start":
        return "bg-[#10b981]";
      case "message":
        return "bg-[#3b82f6]";
      case "interactive":
        return "bg-[#10b981]";
      case "feedback":
        return "bg-[#f59e0b]";
      case "notification":
        return "bg-[#8b5cf6]";
      default:
        return "bg-[#6b7280]";
    }
  };
  const getNodeLabel = (node2) => {
    return node2.data.label || node2.type.charAt(0).toUpperCase() + node2.type.slice(1);
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: `absolute cursor-move select-none transition-all duration-200 ${isSelected ? "z-10" : "z-0"}`,
      style: {
        left: node.position.x,
        top: node.position.y,
        transform: isSelected ? "scale(1.05)" : "scale(1)"
      },
      onClick: (e) => {
        e.stopPropagation();
        onClick();
      },
      onMouseDown: onDragStart,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "div",
          {
            className: `
          w-[160px] min-h-[50px] rounded-lg border p-3 shadow-sm hover:shadow-md transition-all duration-200
          ${getNodeColor(node.type)}
          ${isSelected ? "ring-2 ring-[#3b82f6] ring-offset-2" : ""}
        `,
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 mb-1", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "div",
                  {
                    className: `w-6 h-6 rounded flex items-center justify-center ${getIconBgColor(
                      node.type
                    )}`,
                    children: getNodeIcon(node.type)
                  }
                ),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "font-medium text-sm text-[#1e293b]", children: getNodeLabel(node) })
              ] }),
              node.data.message && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "text-xs text-gray-600 mb-2 line-clamp-2", children: node.data.message }),
              node.data.options && node.data.options.length > 0 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-1", children: [
                node.data.options.slice(0, 2).map((option, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
                  "div",
                  {
                    className: "text-xs bg-white bg-opacity-50 rounded px-2 py-1",
                    children: option
                  },
                  index
                )),
                node.data.options.length > 2 && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-xs text-gray-500", children: [
                  "+",
                  node.data.options.length - 2,
                  " more"
                ] })
              ] }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute -right-1.5 top-1/2 transform -translate-y-1/2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer" }) }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute -left-1.5 top-1/2 transform -translate-y-1/2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-3 h-3 bg-white border-2 border-[#94a3b8] rounded-full hover:border-[#3b82f6] transition-colors cursor-pointer" }) })
            ]
          }
        ),
        isSelected && /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "absolute -top-8 right-0 flex space-x-1", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-gray-50 transition-colors",
              title: "Edit",
              children: "✏️"
            }
          ),
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "button",
            {
              className: "w-6 h-6 bg-white border border-gray-300 rounded text-xs hover:bg-red-50 hover:border-red-300 transition-colors",
              title: "Delete",
              children: "🗑️"
            }
          )
        ] })
      ]
    }
  );
};

const React$2 = await importShared('react');
const {useRef,useState: useState$2} = React$2;
const FlowCanvas = () => {
  const dispatch = useAppDispatch();
  const { currentFlow, selectedNode } = useAppSelector(
    (state) => state.builder
  );
  const canvasRef = useRef(null);
  const [isDragging, setIsDragging] = useState$2(false);
  const [dragOffset, setDragOffset] = useState$2({ x: 0, y: 0 });
  const handleCanvasClick = (e) => {
    if (e.target === canvasRef.current) {
      dispatch(setSelectedNode(null));
    }
  };
  const handleNodeClick = (node) => {
    dispatch(setSelectedNode(node));
  };
  const handleNodeDragStart = (e, node) => {
    setIsDragging(true);
    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left - node.position.x,
        y: e.clientY - rect.top - node.position.y
      });
    }
  };
  const handleNodeDrag = (e, node) => {
    if (!isDragging) return;
    const rect = canvasRef.current?.getBoundingClientRect();
    if (rect) {
      const newPosition = {
        x: e.clientX - rect.left - dragOffset.x,
        y: e.clientY - rect.top - dragOffset.y
      };
      dispatch(
        updateNode({
          ...node,
          position: newPosition
        })
      );
    }
  };
  const handleNodeDragEnd = () => {
    setIsDragging(false);
  };
  const addNewNode = (type, position) => {
    const newNode = {
      id: `${type}-${Date.now()}`,
      type,
      position,
      data: {
        label: type.charAt(0).toUpperCase() + type.slice(1)
      }
    };
    dispatch(addNode(newNode));
  };
  const displayFlow = currentFlow || {
    nodes: [
      {
        id: "start",
        type: "message",
        position: { x: 200, y: 150 },
        data: { label: "Start" }
      },
      {
        id: "message1",
        type: "message",
        position: { x: 450, y: 250 },
        data: { label: "Message" }
      }
    ],
    connections: [
      {
        id: "start-message1",
        source: "start",
        target: "message1"
      }
    ]
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 relative bg-[#fafbfc] overflow-hidden p-4", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-full h-full bg-white rounded-xl shadow-sm border border-[#e2e8f0] relative overflow-hidden", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        ref: canvasRef,
        className: "w-full h-full relative cursor-default",
        onClick: handleCanvasClick,
        onMouseMove: (e) => {
          if (selectedNode && isDragging) {
            handleNodeDrag(e, selectedNode);
          }
        },
        onMouseUp: handleNodeDragEnd,
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: "absolute inset-0 opacity-15",
              style: {
                backgroundImage: `
                linear-gradient(rgba(148,163,184,0.2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(148,163,184,0.2) 1px, transparent 1px)
              `,
                backgroundSize: "24px 24px"
              }
            }
          ),
          displayFlow.nodes.map((node) => /* @__PURE__ */ jsxRuntimeExports.jsx(
            NodeComponent,
            {
              node,
              isSelected: selectedNode?.id === node.id,
              onClick: () => handleNodeClick(node),
              onDragStart: (e) => handleNodeDragStart(e, node)
            },
            node.id
          )),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("svg", { className: "absolute inset-0 pointer-events-none", children: [
            displayFlow.connections.map((connection) => {
              const sourceNode = displayFlow.nodes.find(
                (n) => n.id === connection.source
              );
              const targetNode = displayFlow.nodes.find(
                (n) => n.id === connection.target
              );
              if (!sourceNode || !targetNode) return null;
              const startX = sourceNode.position.x + 80;
              const startY = sourceNode.position.y + 25;
              const endX = targetNode.position.x + 80;
              const endY = targetNode.position.y + 25;
              const midX = (startX + endX) / 2;
              const controlX = midX;
              const controlY = startY;
              return /* @__PURE__ */ jsxRuntimeExports.jsx(
                "path",
                {
                  d: `M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`,
                  stroke: "#94a3b8",
                  strokeWidth: "2",
                  fill: "none",
                  markerEnd: "url(#arrowhead)"
                },
                connection.id
              );
            }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("defs", { children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "marker",
              {
                id: "arrowhead",
                markerWidth: "8",
                markerHeight: "6",
                refX: "7",
                refY: "3",
                orient: "auto",
                children: /* @__PURE__ */ jsxRuntimeExports.jsx("polygon", { points: "0 0, 8 3, 0 6", fill: "#94a3b8" })
              }
            ) })
          ] })
        ]
      }
    ),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute bottom-6 right-6", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
      "button",
      {
        onClick: () => addNewNode("message", { x: 300, y: 200 }),
        className: "w-12 h-12 bg-[#3b82f6] text-white rounded-full shadow-lg hover:bg-[#2563eb] transition-colors flex items-center justify-center",
        title: "Add Node",
        children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-6 h-6",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M12 6v6m0 0v6m0-6h6m-6 0H6"
              }
            )
          }
        )
      }
    ) })
  ] }) });
};

await importShared('react');

const NodesSidebar = () => {
  const nodeCategories = [
    {
      title: "Usage",
      color: "bg-[#3b82f6]",
      nodes: [
        {
          id: "message",
          label: "Message",
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4 text-[#3b82f6]",
              fill: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z" })
            }
          )
        },
        {
          id: "interactive",
          label: "Interactive Message",
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4 text-[#10b981]",
              fill: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" })
            }
          )
        },
        {
          id: "feedback",
          label: "Feedback",
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4 text-[#f59e0b]",
              fill: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" })
            }
          )
        }
      ]
    },
    {
      title: "Utilities",
      color: "bg-[#6366f1]",
      nodes: [
        {
          id: "utilities",
          label: "Utilities",
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4 text-[#6366f1]",
              fill: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" })
            }
          )
        }
      ]
    },
    {
      title: "Marketplace",
      color: "bg-[#8b5cf6]",
      nodes: [
        {
          id: "notification",
          label: "Notification",
          icon: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "svg",
            {
              className: "w-4 h-4 text-[#8b5cf6]",
              fill: "currentColor",
              viewBox: "0 0 24 24",
              children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { d: "M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" })
            }
          )
        }
      ]
    }
  ];
  return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-[280px] p-4 flex flex-col", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "bg-white rounded-xl shadow-lg border border-[#e2e8f0] flex flex-col h-full", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "px-4 py-3 border-b border-[#f1f5f9]", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-sm font-semibold text-[#1e293b]", children: "Nodes" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-1 text-[#94a3b8] hover:text-[#64748b] transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "svg",
        {
          className: "w-4 h-4",
          fill: "none",
          stroke: "currentColor",
          viewBox: "0 0 24 24",
          children: /* @__PURE__ */ jsxRuntimeExports.jsx(
            "path",
            {
              strokeLinecap: "round",
              strokeLinejoin: "round",
              strokeWidth: 2,
              d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            }
          )
        }
      ) })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto p-3 space-y-3", children: nodeCategories.map((category) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
      "div",
      {
        className: "bg-[#f8fafc] rounded-lg border border-[#e2e8f0] p-3",
        children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2 mb-3", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: `w-3 h-3 ${category.color} rounded-full` }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("h4", { className: "text-xs font-semibold text-[#1e293b] uppercase tracking-wide", children: category.title })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-2 gap-2", children: category.nodes.map((node) => /* @__PURE__ */ jsxRuntimeExports.jsx(
            "div",
            {
              className: "bg-white border border-[#e2e8f0] rounded-lg p-3 cursor-pointer hover:shadow-md hover:border-[#3b82f6] transition-all duration-200 group",
              draggable: true,
              onDragStart: (e) => {
                e.dataTransfer.setData("application/reactflow", node.id);
                e.dataTransfer.effectAllowed = "move";
              },
              children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-col items-center space-y-2", children: [
                /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-8 h-8 bg-[#f8fafc] rounded-lg flex items-center justify-center group-hover:bg-[#eff6ff] transition-colors", children: node.icon }),
                /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-xs font-medium text-[#1e293b] text-center leading-tight", children: node.label })
              ] })
            },
            node.id
          )) })
        ]
      },
      category.title
    )) })
  ] }) });
};

const React$1 = await importShared('react');
const {useState: useState$1} = React$1;

await importShared('react');

await importShared('react');
const Builder = () => {
  const { mode } = useAppSelector((state) => state.builder);
  const renderContent = () => {
    switch (mode) {
      case "design":
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-1 overflow-hidden", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(FlowSidebar, {}),
          /* @__PURE__ */ jsxRuntimeExports.jsx(FlowCanvas, {}),
          /* @__PURE__ */ jsxRuntimeExports.jsx(NodesSidebar, {})
        ] });
      case "train":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: "🧠" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Training Mode" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Train your chatbot with conversation data and improve its responses." })
        ] }) });
      case "channels":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: "📱" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Channels" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Configure deployment channels for your chatbot." })
        ] }) });
      case "agent-transfer":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: "👥" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Agent Transfer" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Set up seamless handoff to human agents when needed." })
        ] }) });
      case "integrations":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: "🔗" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Integrations" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Connect your chatbot with external services and APIs." })
        ] }) });
      case "settings":
        return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-12", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: "⚙️" }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "Settings" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Configure your chatbot settings and preferences." })
        ] }) });
      default:
        return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex flex-1 overflow-hidden", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(FlowSidebar, {}),
          /* @__PURE__ */ jsxRuntimeExports.jsx(FlowCanvas, {}),
          /* @__PURE__ */ jsxRuntimeExports.jsx(NodesSidebar, {})
        ] });
    }
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-screen flex bg-[#fafbfc]", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(LeftNavigation, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 flex flex-col", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(BuilderHeader, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(BuilderTabs, {}),
      renderContent()
    ] })
  ] });
};

await importShared('react');
function BuilderPage() {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(Builder, {});
}

await importShared('react');
const ChatList = () => {
  const dispatch = useAppDispatch();
  const { sessions, activeSession, statusFilter, searchQuery } = useAppSelector((state) => state.chat);
  const statusCounts = {
    active: sessions.filter((s) => s.status === "active").length,
    queued: sessions.filter((s) => s.status === "queued").length,
    archived: sessions.filter((s) => s.status === "archived").length,
    missed: sessions.filter((s) => s.status === "missed").length
  };
  const filteredSessions = sessions.filter((session) => {
    const matchesSearch = session.userName.toLowerCase().includes(searchQuery.toLowerCase()) || session.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || session.status === statusFilter;
    return matchesSearch && matchesStatus;
  });
  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "queued":
        return "bg-blue-500";
      case "archived":
        return "bg-gray-400";
      case "missed":
        return "bg-red-500";
      default:
        return "bg-gray-400";
    }
  };
  const getStatusLabel = (status) => {
    switch (status) {
      case "active":
        return "Active";
      case "queued":
        return "Queued";
      case "archived":
        return "Archived";
      case "missed":
        return "Missed";
      default:
        return status;
    }
  };
  const handleSessionClick = (session) => {
    dispatch(setActiveSession(session));
  };
  const handleSearchChange = (e) => {
    dispatch(setSearchQuery(e.target.value));
  };
  const handleStatusFilterChange = (status) => {
    dispatch(setStatusFilter(status));
  };
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-80 bg-white border-r border-gray-200 flex flex-col", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "p-4 border-b border-gray-200", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("h2", { className: "text-xl font-semibold text-gray-900 mb-4", children: "Chats" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex space-x-1 mb-4", children: [
        { key: "active", label: "Active", count: statusCounts.active },
        { key: "queued", label: "Queued", count: statusCounts.queued },
        { key: "archived", label: "Archived", count: statusCounts.archived },
        { key: "missed", label: "Missed", count: statusCounts.missed }
      ].map((filter) => /* @__PURE__ */ jsxRuntimeExports.jsxs(
        "button",
        {
          onClick: () => handleStatusFilterChange(filter.key),
          className: `
                flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium transition-colors
                ${statusFilter === filter.key ? "bg-blue-100 text-blue-700" : "bg-gray-100 text-gray-600 hover:bg-gray-200"}
              `,
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { children: filter.label }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "bg-white bg-opacity-70 px-1 rounded-full", children: filter.count })
          ]
        },
        filter.key
      )) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "h-4 w-4 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "input",
          {
            type: "text",
            placeholder: "Search conversations...",
            value: searchQuery,
            onChange: handleSearchChange,
            className: "block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto", children: filteredSessions.length === 0 ? /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center py-8", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-6 h-6 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" }) }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-500", children: "No conversations found" })
    ] }) : /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "divide-y divide-gray-100", children: filteredSessions.map((session) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        onClick: () => handleSessionClick(session),
        className: `
                  p-4 cursor-pointer hover:bg-gray-50 transition-colors
                  ${activeSession?.id === session.id ? "bg-blue-50 border-r-2 border-blue-500" : ""}
                `,
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start space-x-3", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium text-gray-600", children: session.userName.split(" ").map((n) => n[0]).join("") }) }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: `absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(session.status)}` })
          ] }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 min-w-0", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm font-medium text-gray-900 truncate", children: session.userName }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-xs text-gray-500", children: session.timestamp })
            ] }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-600 truncate mt-1", children: session.lastMessage }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex items-center justify-between mt-2", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: `
                        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        ${session.status === "active" ? "bg-green-100 text-green-800" : session.status === "queued" ? "bg-blue-100 text-blue-800" : session.status === "archived" ? "bg-gray-100 text-gray-800" : "bg-red-100 text-red-800"}
                      `, children: getStatusLabel(session.status) }) })
          ] })
        ] })
      },
      session.id
    )) }) })
  ] });
};

const React = await importShared('react');
const {useState} = React;
const ChatWindow = () => {
  const dispatch = useAppDispatch();
  const { activeSession } = useAppSelector((state) => state.chat);
  const [inputMessage, setInputMessage] = useState("");
  const handleSendMessage = () => {
    if (inputMessage.trim() && activeSession) {
      const newMessage = {
        id: Date.now().toString(),
        content: inputMessage,
        sender: "bot",
        timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
      };
      dispatch(addMessage({
        sessionId: activeSession.id,
        message: newMessage
      }));
      setInputMessage("");
    }
  };
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  const handleEndChat = () => {
    if (activeSession) {
      dispatch(updateSessionStatus({
        sessionId: activeSession.id,
        status: "archived"
      }));
    }
  };
  if (!activeSession) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center bg-gray-50", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-8 h-8 text-gray-400", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" }) }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "No conversation selected" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-500", children: "Select a conversation from the sidebar to start chatting." })
    ] }) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1 flex flex-col bg-white", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200 bg-white", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center justify-between", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm font-medium text-gray-600", children: activeSession.userName.split(" ").map((n) => n[0]).join("") }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: activeSession.userName }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-500", children: "A logged user" })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", title: "Call", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", title: "Video call", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: handleEndChat,
            className: "px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors",
            children: "END CHAT"
          }
        ),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" }),
          /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" })
        ] }) })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 overflow-y-auto p-4 space-y-4", children: activeSession.messages.map((message) => /* @__PURE__ */ jsxRuntimeExports.jsx(
      "div",
      {
        className: `flex ${message.sender === "user" ? "justify-end" : "justify-start"}`,
        children: /* @__PURE__ */ jsxRuntimeExports.jsxs(
          "div",
          {
            className: `
                max-w-xs lg:max-w-md px-4 py-2 rounded-lg text-sm
                ${message.sender === "user" ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-900"}
              `,
            children: [
              /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: message.content }),
              /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: `text-xs mt-1 ${message.sender === "user" ? "text-blue-100" : "text-gray-500"}`, children: message.timestamp })
            ]
          }
        )
      },
      message.id
    )) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-t border-gray-200 bg-white", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", title: "Magic Write", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm", children: "✨" }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", title: "Translation", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-sm", children: "🌐" }) })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 relative", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
        "input",
        {
          type: "text",
          value: inputMessage,
          onChange: (e) => setInputMessage(e.target.value),
          onKeyPress: handleKeyPress,
          placeholder: "Hello! How can I assist you today?",
          className: "w-full px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        }
      ) }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex space-x-2", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "p-2 text-gray-400 hover:text-gray-600 transition-colors", children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" }) }) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx(
          "button",
          {
            onClick: handleSendMessage,
            disabled: !inputMessage.trim(),
            className: "p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx("svg", { className: "w-5 h-5", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsxRuntimeExports.jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M12 19l9 2-9-18-9 18 9-2zm0 0v-8" }) })
          }
        )
      ] })
    ] }) })
  ] });
};

await importShared('react');
function ChatPage() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "h-screen flex bg-gray-50", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(ChatList, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(ChatWindow, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "w-80 bg-white border-l border-gray-200 flex flex-col", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-4 border-b border-gray-200", children: /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-900", children: "Comments" }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "flex-1 flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx(
          "svg",
          {
            className: "w-8 h-8 text-gray-400",
            fill: "none",
            stroke: "currentColor",
            viewBox: "0 0 24 24",
            children: /* @__PURE__ */ jsxRuntimeExports.jsx(
              "path",
              {
                strokeLinecap: "round",
                strokeLinejoin: "round",
                strokeWidth: 2,
                d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              }
            )
          }
        ) }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-medium text-gray-900 mb-2", children: "No comments - yet" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs("p", { className: "text-gray-500 text-sm mb-4", children: [
          "Save feedback and comments, or start a",
          /* @__PURE__ */ jsxRuntimeExports.jsx("br", {}),
          "discussion in this conversation."
        ] }),
        /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-gray-400 text-xs", children: "All agent comments" })
      ] }) })
    ] })
  ] });
}

await importShared('react');

const SettingsCard = ({
  title,
  description,
  icon,
  children,
  onClick
}) => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(
    "div",
    {
      className: `bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 ${onClick ? "cursor-pointer" : ""}`,
      onClick,
      children: [
        /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-start space-x-4 mb-4", children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center", children: /* @__PURE__ */ jsxRuntimeExports.jsx("span", { className: "text-2xl", children: icon }) }),
          /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx("h3", { className: "text-lg font-semibold text-gray-900 mb-2", children: title }),
            /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-sm text-gray-600", children: description })
          ] })
        ] }),
        children && /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "mt-4", children })
      ]
    }
  );
};

await importShared('react');
function SettingsPage() {
  const settingsCards = [
    {
      title: "Language",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "🌐"
    },
    {
      title: "NLU",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "🧠"
    },
    {
      title: "LLM Configuration",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "⚙️"
    },
    {
      title: "Content Resources",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "📚"
    },
    {
      title: "Personalization",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor",
      icon: "👤"
    }
  ];
  const headerActions = /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "flex items-center space-x-3", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors", children: "🔍 PREVIEW" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("button", { className: "px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors", children: "📤 PUBLISH" })
  ] });
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    Layout$1,
    {
      title: "Untitled_25_04_25_0950",
      subtitle: "No description",
      headerActions,
      children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "p-6", children: /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", children: settingsCards.map((card, index) => /* @__PURE__ */ jsxRuntimeExports.jsx(
        SettingsCard,
        {
          title: card.title,
          description: card.description,
          icon: card.icon,
          onClick: () => console.log(`Clicked ${card.title}`)
        },
        index
      )) }) })
    }
  );
}

await importShared('react');

const {BrowserRouter,Routes,Route,Outlet} = await importShared('react-router-dom');

const {Provider} = await importShared('react-redux');
function Layout({ children }) {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("html", { lang: "en", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs("head", { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("meta", { charSet: "utf-8" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("meta", { name: "viewport", content: "width=device-width, initial-scale=1" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("title", { children: "NeuraTalk AI - Dashboard" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("link", { rel: "preconnect", href: "https://fonts.googleapis.com" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "link",
        {
          rel: "preconnect",
          href: "https://fonts.gstatic.com",
          crossOrigin: "anonymous"
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(
        "link",
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        }
      )
    ] }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("body", { children })
  ] });
}
function App() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Provider, { store, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(AuthHydrator, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(BrowserRouter, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(Routes, { children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/", element: /* @__PURE__ */ jsxRuntimeExports.jsx(Home, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/agents", element: /* @__PURE__ */ jsxRuntimeExports.jsx(Agents, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/agents/new", element: /* @__PURE__ */ jsxRuntimeExports.jsx(NewAgents, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/builder/:id", element: /* @__PURE__ */ jsxRuntimeExports.jsx(BuilderPage, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/chat", element: /* @__PURE__ */ jsxRuntimeExports.jsx(ChatPage, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "/settings/:id", element: /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsPage, {}) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Route, { path: "*", element: /* @__PURE__ */ jsxRuntimeExports.jsx(ErrorBoundary, {}) })
    ] }) })
  ] });
}
function ErrorBoundary() {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("main", { className: "pt-16 p-4 container mx-auto", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("h1", { children: "404" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "The requested page could not be found." })
  ] });
}

export { ErrorBoundary, Layout, App as default };
