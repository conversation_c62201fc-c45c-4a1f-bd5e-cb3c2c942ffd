# Webpack Migration Guide

This project has been migrated from Vite to Webpack with Module Federation support.

## Changes Made

### 1. Package.json Updates
- Removed Vite-specific dependencies:
  - `vite`
  - `@vitejs/plugin-react`
  - `@originjs/vite-plugin-federation`
  - `vite-tsconfig-paths`
  - `@tailwindcss/vite`

- Added Webpack dependencies:
  - `webpack`, `webpack-cli`, `webpack-dev-server`
  - `@module-federation/webpack`
  - `babel-loader`, `ts-loader`
  - `css-loader`, `style-loader`, `mini-css-extract-plugin`
  - `html-webpack-plugin`
  - Babel presets for React and TypeScript

### 2. Configuration Files
- **webpack.config.js**: Main Webpack configuration with Module Federation
- **babel.config.js**: Babel configuration for React and TypeScript
- **postcss.config.js**: PostCSS configuration for Tailwind CSS
- **src/index.ts**: New entry point for Webpack

### 3. Updated Scripts
```json
{
  "build": "webpack --mode production",
  "build:dev": "webpack --mode development", 
  "dev": "webpack serve --mode development",
  "start": "webpack serve --mode production --port 5173",
  "typecheck": "tsc"
}
```

## Installation

```bash
npm install
```

## Development

```bash
npm run dev
```

This will start the development server on http://localhost:5173

## Production Build

```bash
npm run build
```

## Module Federation

The micro-frontend exposes the following modules:

- `./` - Main bootstrap entry
- `./MicroFrontendDemo` - Demo component
- `./App` - Root application component
- `./routes` - Router configuration
- `./store` - Redux store
- `./RouterExample` - Router example component
- `./Home` - Home page component
- `./Agents` - Agents page component
- `./NewAgent` - New agent page component

### Shared Dependencies

The following dependencies are shared across micro-frontends:
- React (singleton, eager)
- React DOM (singleton, eager)
- React Router DOM (singleton, eager)
- Redux Toolkit (singleton)
- React Redux (singleton)

## Key Features

- **Module Federation**: Full support for micro-frontend architecture
- **TypeScript**: Complete TypeScript support with proper type checking
- **Tailwind CSS**: Integrated Tailwind CSS with PostCSS processing
- **Hot Module Replacement**: Fast development with HMR
- **Code Splitting**: Automatic code splitting for optimal loading
- **CORS Support**: Proper CORS headers for cross-origin requests

## Troubleshooting

### Common Issues

1. **Module not found errors**: Ensure all dependencies are installed with `npm install`
2. **TypeScript errors**: Run `npm run typecheck` to verify TypeScript configuration
3. **CSS not loading**: Check that PostCSS and Tailwind are properly configured
4. **Federation errors**: Verify that shared dependencies versions match across micro-frontends

### Development Tips

- Use `npm run build:dev` for development builds with source maps
- Check the browser console for Module Federation loading errors
- Ensure the remoteEntry.js file is accessible at the expected URL
